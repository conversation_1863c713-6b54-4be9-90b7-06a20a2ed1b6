GAYRİMENKUL KARAR DESTEK SİSTEMİ - TEZ KAPSAMLI ANALİZ RAPORU
======================================================================
Analiz Tarihi: 2025-06-04 15:02:21
Toplam Kayıt Sayısı: 20,284

1. VERI YÜKLEME VE EDA
------------------------

AÇIKLAMA:
Gayrimenkul veri setinin yüklenmesi ve temel özelliklerinin incelenmesi

YÖNTEM SEÇİM NEDENİ:
Veri setini anlamak, kalitesini değerlendirmek ve sonraki adımları planlamak için EDA yapıldı

SONUÇLAR:
• total_records: 20,326
• total_features: 15
• numeric_features: 6
• categorical_features: 9
• missing_data_percentage: 10.508

SONUÇ VE DEĞERLENDİRME:
Toplam 20,326 gayrimenkul verisi analiz edilecek. Veri setinde 9 kategorik ve 6 sayısal özellik bulunuyor.

======================================================================

2. VERI ÖN İŞLEME
-------------------

AÇIKLAMA:
Geçersiz değerlerin temizlenmesi ve veri kalitesinin artırılması

YÖNTEM SEÇİM NEDENİ:
Makine öğrenmesi algoritmalarının doğru çalışması için temiz veri gereklidir. Geçersiz değerler model performansını olumsuz etkiler.

SONUÇLAR:
• initial_records: 20,326
• final_records: 20,284
• removed_records: 42
• removal_percentage: 0.207
• data_quality_score: 99.793

SONUÇ VE DEĞERLENDİRME:
Veri temizleme sonucunda 42 geçersiz kayıt kaldırıldı. Veri kalitesi %99.8 seviyesinde.

======================================================================

3. ÖZELLIK MÜHENDISLIĞI
-------------------------

AÇIKLAMA:
Domain bilgisi kullanarak yeni anlamlı özellikler oluşturma

YÖNTEM SEÇİM NEDENİ:
Ham özellikler tek başına yeterli bilgi vermeyebilir. Özellik kombinasyonları ve oranlar daha güçlü tahmin gücü sağlar.

SONUÇLAR:
• new_features_created: 7
• total_numeric_features: 12
• total_categorical_features: 9
• total_features: 21

SONUÇ VE DEĞERLENDİRME:
7 yeni özellik oluşturuldu. Bu özellikler gayrimenkul değerlendirmesinde kritik metrikleri temsil ediyor.

======================================================================

4. ANOMALI TESPITI
--------------------

AÇIKLAMA:
Isolation Forest ve One-Class SVM ile anormal gayrimenkul fiyatlarının tespiti

YÖNTEM SEÇİM NEDENİ:
Anormal fiyatlı gayrimenkulleri tespit ederek veri kalitesini artırmak ve piyasa dışı fiyatlandırmaları belirlemek için kullanıldı.

SONUÇLAR:
• isolation_forest_anomalies: 2029
• isolation_forest_percentage: 10.003
• one_class_svm_anomalies: 2028
• one_class_svm_percentage: 9.998
• common_anomalies: 1311
• total_unique_anomalies: 2,746

SONUÇ VE DEĞERLENDİRME:
İki farklı yöntemle anomali tespiti yapıldı. Isolation Forest %10.0, One-Class SVM %10.0 anomali tespit etti.

======================================================================

5. KÜMELEME ANALIZI
---------------------

AÇIKLAMA:
K-Means, Agglomerative ve DBSCAN algoritmaları ile gayrimenkul segmentasyonu

YÖNTEM SEÇİM NEDENİ:
Benzer özelliklere sahip gayrimenkulleri gruplamak ve piyasa segmentlerini belirlemek için farklı kümeleme algoritmaları kullanıldı.

SONUÇLAR:
• kmeans: {'n_clusters': 3, 'silhouette_score': 0.15579939151823285, 'inertia': 250152.85558444585, 'labels': array([2, 2, 2, ..., 2, 2, 2]), 'cluster_distribution': {0: 8035, 1: 1, 2: 12248}}
• agglomerative: {'n_clusters': 3, 'silhouette_score': 0.33217935895636275, 'labels': array([2, 2, 2, ..., 2, 2, 2], dtype=int64)}
• dbscan: {'n_clusters': 4, 'n_noise': 20237, 'eps': 0.3, 'min_samples': 10, 'silhouette_score': -0.1984503708759827, 'labels': array([-1, -1, -1, ..., -1, -1, -1], dtype=int64)}

SONUÇ VE DEĞERLENDİRME:
Üç farklı kümeleme algoritması uygulandı. En iyi performans K-Means ile 3 küme oluşturularak elde edildi (Silhouette: 0.156).

======================================================================

GENEL DEĞERLENDİRME VE KARŞILAŞTIRMA
========================================

1. VERİ KALİTESİ:
• Veri kalite skoru: %99.8
• Değerlendirme: Çok iyi veri kalitesi

2. ÖZELLİK MÜHENDİSLİĞİ ETKİSİ:
• Oluşturulan yeni özellik sayısı: 7
• Toplam özellik sayısı: 21
• Özellik artış oranı: %33.3

3. ANOMALİ TESPİTİ KARŞILAŞTIRMASI:
• Isolation Forest: %10.0 anomali
• One-Class SVM: %10.0 anomali
• Yöntemler arası fark: %0.0
• Değerlendirme: Yöntemler tutarlı sonuç veriyor

4. KÜMELEME ALGORİTMALARI KARŞILAŞTIRMASI:
• K-Means Silhouette: 0.156
• Agglomerative Silhouette: 0.332
• DBSCAN: Silhouette hesaplanamadı
• En iyi kümeleme yöntemi: Agglomerative

5. ÖNERİLER:
• Veri kalitesini artırmak için düzenli veri temizleme yapılmalı
• Özellik mühendisliği ile model performansı artırılabilir
• Anomali tespiti ile piyasa dışı fiyatlar belirlenebilir
• Kümeleme analizi ile piyasa segmentasyonu yapılabilir
• Farklı algoritmaların ensemble kullanımı değerlendirilebilir
