"""
Gayrimenkul Karar Destek Sistemi - <PERSON>üm makine öğrenmesi ve veri madenciliği yöntemlerini içeren tek dosya
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, OneHotEncoder, LabelEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import accuracy_score, mean_squared_error, r2_score, silhouette_score
from sklearn.svm import LinearSVC
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor, IsolationForest
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN
from sklearn.mixture import GaussianMixture
from sklearn.decomposition import PCA
from sklearn.feature_selection import SelectKBest, f_regression
import joblib
import time
import warnings
import os

# XGBoost kontrolü
try:
    from xgboost import XGBClassifier, XGBRegressor
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost kütüphanesi bulunamadı. XGBoost modelleri devre dışı.")

# Uyarıları kapat
warnings.filterwarnings('ignore')

class GayrimenkulDataProcessor:
    """Gayrimenkul verilerini işleme sınıfı"""

    def __init__(self):
        self.df = None
        self.numeric_features = []
        self.categorical_features = []
        self.target_column = 'Fiyat'

    def load_data(self, file_path, data_limit=None):
        """Veri dosyasını yükle"""
        print(f"📂 Veri yükleniyor: {file_path}")
        try:
            self.df = pd.read_csv(file_path)
            if data_limit:
                self.df = self.df.sample(min(data_limit, len(self.df)), random_state=42)
            print(f"✅ Veri yüklendi: {len(self.df)} satır, {len(self.df.columns)} sütun")
            return self.df
        except Exception as e:
            print(f"❌ Veri yükleme hatası: {e}")
            # Örnek veri oluştur
            print("⚠️ Örnek veri oluşturuluyor...")
            self.create_sample_data()
            return self.df

    def create_sample_data(self, rows=1000):
        """Örnek veri oluştur"""
        np.random.seed(42)
        self.df = pd.DataFrame({
            'price': np.random.normal(500000, 150000, rows),
            'area': np.random.normal(120, 30, rows),
            'rooms': np.random.randint(1, 6, rows),
            'bathrooms': np.random.randint(1, 4, rows),
            'age': np.random.randint(0, 40, rows),
            'floor': np.random.randint(1, 20, rows),
            'location': np.random.choice(['A', 'B', 'C', 'D', 'E'], rows),
            'has_parking': np.random.choice([0, 1], rows),
            'has_elevator': np.random.choice([0, 1], rows),
            'has_garden': np.random.choice([0, 1], rows),
        })
        print(f"✅ Örnek veri oluşturuldu: {len(self.df)} satır")

    def clean_data(self):
        """Veri temizleme"""
        print("🧹 Veri temizleniyor...")
        if self.df is None:
            print("❌ Veri yok!")
            return None

        # Eksik değerleri kontrol et
        missing = self.df.isnull().sum()
        if missing.sum() > 0:
            print(f"⚠️ Eksik değerler tespit edildi: {missing[missing > 0]}")
            # Sayısal sütunlardaki eksik değerleri medyan ile doldur
            for col in self.df.select_dtypes(include=['float64', 'int64']).columns:
                self.df[col].fillna(self.df[col].median(), inplace=True)

            # Kategorik sütunlardaki eksik değerleri mod ile doldur
            for col in self.df.select_dtypes(include=['object']).columns:
                self.df[col].fillna(self.df[col].mode()[0], inplace=True)

        # Aykırı değerleri kontrol et (sayısal sütunlar için)
        for col in self.df.select_dtypes(include=['float64', 'int64']).columns:
            Q1 = self.df[col].quantile(0.25)
            Q3 = self.df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers = ((self.df[col] < lower_bound) | (self.df[col] > upper_bound)).sum()
            if outliers > 0:
                print(f"⚠️ {col} sütununda {outliers} aykırı değer tespit edildi")
                # Aykırı değerleri sınırla (capping)
                self.df[col] = np.where(self.df[col] < lower_bound, lower_bound, self.df[col])
                self.df[col] = np.where(self.df[col] > upper_bound, upper_bound, self.df[col])

        print("✅ Veri temizleme tamamlandı")
        return self.df

    def feature_engineering(self):
        """Özellik mühendisliği"""
        print("🔧 Özellik mühendisliği yapılıyor...")
        if self.df is None:
            print("❌ Veri yok!")
            return None

        # Sayısal ve kategorik özellikleri belirle
        self.numeric_features = self.df.select_dtypes(include=['float64', 'int64']).columns.tolist()
        self.categorical_features = self.df.select_dtypes(include=['object']).columns.tolist()

        # Hedef sütunu çıkar
        print(f"🔍 Debug: target_column = '{self.target_column}'")
        print(f"🔍 Debug: numeric_features = {self.numeric_features}")
        print(f"🔍 Debug: target_column in numeric_features = {self.target_column in self.numeric_features}")

        if self.target_column in self.numeric_features:
            self.numeric_features.remove(self.target_column)
            print(f"✅ Hedef sütun '{self.target_column}' sayısal özelliklerden çıkarıldı")
        else:
            print(f"⚠️ Hedef sütun '{self.target_column}' sayısal özellikler listesinde bulunamadı")

        print(f"📊 Sayısal özellikler: {self.numeric_features}")
        print(f"📊 Kategorik özellikler: {self.categorical_features}")

        # Yeni özellikler oluştur
        if 'Net_Metrekare' in self.df.columns and 'Oda_Sayısı' in self.df.columns:
            self.df['area_per_room'] = self.df['Net_Metrekare'] / (self.df['Oda_Sayısı'] + 1)
            self.numeric_features.append('area_per_room')

        # Binanın yaşı kategorik olduğu için sayısal bir özellik oluşturmayacağız

        # Kategorik değişkenleri sayısallaştır
        for col in self.categorical_features:
            if self.df[col].nunique() < 10:  # Düşük kardinalite
                # One-hot encoding
                dummies = pd.get_dummies(self.df[col], prefix=col, drop_first=True)
                self.df = pd.concat([self.df, dummies], axis=1)
                # Yeni sütunları numeric_features'a ekle
                self.numeric_features.extend(dummies.columns.tolist())
            else:  # Yüksek kardinalite
                # Label encoding
                le = LabelEncoder()
                self.df[f'{col}_encoded'] = le.fit_transform(self.df[col])
                self.numeric_features.append(f'{col}_encoded')

        print("✅ Özellik mühendisliği tamamlandı")
        return self.df

    def analyze_correlations(self):
        """Korelasyon analizi"""
        print("📊 Korelasyon analizi yapılıyor...")
        if self.df is None:
            print("❌ Veri yok!")
            return None

        # Sadece sayısal sütunlar için korelasyon matrisi (hedef sütun dahil)
        # Hedef sütunu tekrar ekle çünkü feature_engineering'de çıkarılmış
        all_numeric_cols = self.numeric_features.copy()
        if self.target_column not in all_numeric_cols:
            all_numeric_cols.append(self.target_column)

        # Sadece DataFrame'de bulunan sütunları kullan
        available_cols = [col for col in all_numeric_cols if col in self.df.columns]
        print(f"🔍 Korelasyon analizi için kullanılacak sütunlar: {available_cols}")

        corr_matrix = self.df[available_cols].corr()

        # Fiyat ile korelasyonlar
        if self.target_column in corr_matrix.columns:
            price_correlations = corr_matrix[self.target_column].sort_values(ascending=False)
            print("\nFiyat ile korelasyonlar:")
            print(price_correlations)

        return corr_matrix

    def analyze_categorical_impact(self):
        """Kategorik değişkenlerin etkisini analiz et"""
        print("📊 Kategorik değişken analizi yapılıyor...")
        if self.df is None:
            print("❌ Veri yok!")
            return None

        results = {}
        for col in self.categorical_features:
            # Kategorik değişkene göre fiyat ortalamaları
            impact = self.df.groupby(col)[self.target_column].agg(['mean', 'count']).sort_values('mean', ascending=False)
            results[col] = impact
            print(f"\n{col} değişkeninin fiyat üzerindeki etkisi:")
            print(impact)

        return results

    def prepare_features(self):
        """Özellikleri ve hedef değişkeni hazırla"""
        print("🎯 Özellikler hazırlanıyor...")
        if self.df is None:
            print("❌ Veri yok!")
            return None, None

        # Kategorik sütunları çıkar (zaten dönüştürüldü)
        X = self.df[self.numeric_features]
        y = self.df[self.target_column]

        print(f"✅ Özellikler hazırlandı: {X.shape[1]} özellik")
        return X, y

    def prepare_classification_target(self, y):
        """Regresyon hedefini sınıflandırma için hazırla"""
        print("🎯 Sınıflandırma hedefi hazırlanıyor...")
        # Fiyatı kategorilere ayır
        bins = [0, y.quantile(0.33), y.quantile(0.66), float('inf')]
        labels = [0, 1, 2]  # Düşük, Orta, Yüksek
        y_class = pd.cut(y, bins=bins, labels=labels)

        print(f"✅ Sınıflandırma hedefi hazırlandı: {y_class.value_counts().to_dict()}")
        return y_class

    def create_preprocessor(self):
        """Veri ön işleme pipeline'ı oluştur"""
        print("🔄 Veri ön işleme pipeline'ı oluşturuluyor...")

        # Sayısal özellikler için işlemler
        numeric_transformer = Pipeline(steps=[
            ('imputer', SimpleImputer(strategy='median')),
            ('scaler', StandardScaler())
        ])

        # Kategorik özellikler için işlemler
        categorical_transformer = Pipeline(steps=[
            ('imputer', SimpleImputer(strategy='most_frequent')),
            ('onehot', OneHotEncoder(handle_unknown='ignore'))
        ])

        # Tüm özellikleri birleştir
        preprocessor = ColumnTransformer(
            transformers=[
                ('num', numeric_transformer, self.numeric_features),
                ('cat', categorical_transformer, self.categorical_features)
            ])

        print("✅ Veri ön işleme pipeline'ı oluşturuldu")
        return preprocessor

    def create_interaction_features(self, df):
        """Etkileşim özellikleri oluştur"""
        print("🔄 Etkileşim özellikleri oluşturuluyor...")

        df_enhanced = df.copy()

        # Alan ve oda sayısı etkileşimi
        if 'area' in df.columns and 'rooms' in df.columns:
            df_enhanced['area_per_room'] = df['area'] / (df['rooms'] + 1)

        # Yaş ve fiyat etkileşimi
        if 'age' in df.columns and 'price' in df.columns:
            df_enhanced['price_per_age'] = df['price'] / (df['age'] + 1)

        # Kat ve asansör etkileşimi
        if 'floor' in df.columns and 'has_elevator' in df.columns:
            df_enhanced['high_floor_no_elevator'] = ((df['floor'] > 3) & (df['has_elevator'] == 0)).astype(int)

        print(f"✅ {len(df_enhanced.columns) - len(df.columns)} yeni etkileşim özelliği oluşturuldu")
        return df_enhanced


class GayrimenkulMLModels:
    """Gayrimenkul makine öğrenmesi modelleri sınıfı"""

    def __init__(self, preprocessor):
        self.preprocessor = preprocessor
        self.models = {}
        self.results = {}

    def train_classification_models(self, X, y):
        """Sınıflandırma modellerini eğit"""
        print("🤖 Sınıflandırma modelleri eğitiliyor...")

        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

        # 1. LinearSVC (Grid Search ile)
        print("   📊 LinearSVC...")
        pipe_svc = Pipeline([('pre', self.preprocessor), ('svc', LinearSVC(max_iter=10000, random_state=42))])
        param_svc = {'svc__C': [0.01, 0.1, 1, 10]}
        grid_svc = GridSearchCV(pipe_svc, param_svc, cv=5, scoring='accuracy', n_jobs=-1)
        grid_svc.fit(X_train, y_train)

        y_pred_svc = grid_svc.predict(X_test)
        self.models['LinearSVC'] = grid_svc
        self.results['LinearSVC'] = {
            'accuracy': accuracy_score(y_test, y_pred_svc),
            'best_params': grid_svc.best_params_
        }

        # 2. Random Forest (Grid Search ile)
        print("   🌲 Random Forest...")
        pipe_rf = Pipeline([('pre', self.preprocessor), ('rf', RandomForestClassifier(random_state=42))])
        param_rf = {'rf__n_estimators': [50, 100], 'rf__max_depth': [10, 20, None]}
        grid_rf = GridSearchCV(pipe_rf, param_rf, cv=5, scoring='accuracy', n_jobs=-1)
        grid_rf.fit(X_train, y_train)

        y_pred_rf = grid_rf.predict(X_test)
        self.models['RandomForest'] = grid_rf
        self.results['RandomForest'] = {
            'accuracy': accuracy_score(y_test, y_pred_rf),
            'best_params': grid_rf.best_params_
        }

        # 3. XGBoost (eğer mevcut ise)
        if XGBOOST_AVAILABLE:
            print("   🚀 XGBoost...")
            pipe_xgb = Pipeline([('pre', self.preprocessor), ('xgb', XGBClassifier(random_state=42, eval_metric='logloss'))])
            pipe_xgb.fit(X_train, y_train)

            y_pred_xgb = pipe_xgb.predict(X_test)
            self.models['XGBoost'] = pipe_xgb
            self.results['XGBoost'] = {
                'accuracy': accuracy_score(y_test, y_pred_xgb)
            }

        # 4. Stacking Ensemble
        print("   🔗 Stacking Ensemble...")
        base_models = [
            ('svc', LinearSVC(max_iter=10000, random_state=42)),
            ('rf', RandomForestClassifier(n_estimators=50, random_state=42))
        ]

        if XGBOOST_AVAILABLE:
            base_models.append(('xgb', XGBClassifier(random_state=42, eval_metric='logloss')))

        # Meta-model olarak RandomForest kullan
        from sklearn.ensemble import StackingClassifier
        stacking = StackingClassifier(
            estimators=base_models,
            final_estimator=RandomForestClassifier(n_estimators=50, random_state=42),
            cv=5
        )

        pipe_stack = Pipeline([('pre', self.preprocessor), ('stack', stacking)])
        pipe_stack.fit(X_train, y_train)

        y_pred_stack = pipe_stack.predict(X_test)
        self.models['Stacking'] = pipe_stack
        self.results['Stacking'] = {
            'accuracy': accuracy_score(y_test, y_pred_stack)
        }

        print("✅ Sınıflandırma modelleri eğitimi tamamlandı!")
        return X_test, y_test

    def train_regression_model(self, X, y):
        """Regresyon modelini eğit (fiyat tahmini)"""
        print("📈 Regresyon modeli eğitiliyor...")

        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # XGBoost Regressor (eğer mevcut ise)
        if XGBOOST_AVAILABLE:
            print("   🚀 XGBoost Regressor...")
            pipe_xgb = Pipeline([('pre', self.preprocessor), ('xgb', XGBRegressor(random_state=42))])
            pipe_xgb.fit(X_train, y_train)

            y_pred = pipe_xgb.predict(X_test)
            self.models['XGBRegressor'] = pipe_xgb
            self.results['XGBRegressor'] = {
                'r2': r2_score(y_test, y_pred),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred))
            }
        else:
            # Fallback olarak Random Forest Regressor
            print("   🌲 Random Forest Regressor (fallback)...")
            pipe_rf = Pipeline([('pre', self.preprocessor), ('rf', RandomForestRegressor(random_state=42))])
            pipe_rf.fit(X_train, y_train)

            y_pred = pipe_rf.predict(X_test)
            self.models['RFRegressor'] = pipe_rf
            self.results['RFRegressor'] = {
                'r2': r2_score(y_test, y_pred),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred))
            }

        print("✅ Regresyon modeli eğitimi tamamlandı!")
        return X_test, y_test, y_pred

    def train_clustering_models(self, X):
        """Kümeleme modellerini eğit"""
        print("🔍 Kümeleme modelleri eğitiliyor...")

        # Veriyi ön işle
        X_processed = self.preprocessor.fit_transform(X)

        # Eğer sparse matris ise dense formata dönüştür
        if hasattr(X_processed, "toarray"):
            X_processed = X_processed.toarray()

        # Optimal küme sayısını belirle (Elbow yöntemi)
        inertia = []
        K_range = range(2, 11)
        for k in K_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(X_processed)
            inertia.append(kmeans.inertia_)

        # Optimal k değerini belirle (basit yaklaşım)
        optimal_k = 3  # Varsayılan değer

        # 1. K-Means
        print(f"   🔵 K-Means (k={optimal_k})...")
        kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
        kmeans.fit(X_processed)

        self.models['KMeans'] = kmeans
        self.results['KMeans'] = {
            'silhouette': silhouette_score(X_processed, kmeans.labels_),
            'inertia': kmeans.inertia_,
            'labels': kmeans.labels_
        }

        # 2. Agglomerative Clustering
        print("   🔵 Agglomerative Clustering...")
        agg = AgglomerativeClustering(n_clusters=optimal_k)
        agg_labels = agg.fit_predict(X_processed)

        self.models['AgglomerativeClustering'] = agg
        self.results['AgglomerativeClustering'] = {
            'silhouette': silhouette_score(X_processed, agg_labels),
            'labels': agg_labels
        }

        # 3. DBSCAN
        print("   🔵 DBSCAN...")
        dbscan = DBSCAN(eps=0.5, min_samples=5)
        dbscan_labels = dbscan.fit_predict(X_processed)

        # DBSCAN -1 etiketleri içerebilir (gürültü noktaları)
        valid_labels = dbscan_labels != -1
        if np.sum(valid_labels) > 1 and len(np.unique(dbscan_labels[valid_labels])) > 1:
            silhouette = silhouette_score(X_processed[valid_labels], dbscan_labels[valid_labels])
        else:
            silhouette = -1  # Geçersiz silhouette skoru

        self.models['DBSCAN'] = dbscan
        self.results['DBSCAN'] = {
            'silhouette': silhouette,
            'labels': dbscan_labels,
            'n_clusters': len(np.unique(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)
        }

        # 4. Gaussian Mixture Model
        print("   🔵 Gaussian Mixture Model...")
        gmm = GaussianMixture(n_components=optimal_k, random_state=42)
        gmm_labels = gmm.fit_predict(X_processed)

        self.models['GaussianMixture'] = gmm
        self.results['GaussianMixture'] = {
            'silhouette': silhouette_score(X_processed, gmm_labels),
            'labels': gmm_labels,
            'bic': gmm.bic(X_processed)
        }

        print("✅ Kümeleme modelleri eğitimi tamamlandı!")
        return X_processed

    def train_anomaly_detection(self, X):
        """Anomali tespiti modellerini eğit"""
        print("🔍 Anomali tespiti modelleri eğitiliyor...")

        # Veriyi ön işle
        X_processed = self.preprocessor.fit_transform(X)

        # Eğer sparse matris ise dense formata dönüştür
        if hasattr(X_processed, "toarray"):
            X_processed = X_processed.toarray()

        # 1. Isolation Forest
        print("   🌲 Isolation Forest...")
        iso_forest = IsolationForest(contamination=0.05, random_state=42)
        iso_forest.fit(X_processed)

        # -1: anomali, 1: normal
        anomaly_scores = iso_forest.decision_function(X_processed)
        anomaly_labels = iso_forest.predict(X_processed)

        self.models['IsolationForest'] = iso_forest
        self.results['IsolationForest'] = {
            'anomaly_scores': anomaly_scores,
            'anomaly_labels': anomaly_labels,
            'anomaly_count': np.sum(anomaly_labels == -1)
        }

        # 2. One-Class SVM
        from sklearn.svm import OneClassSVM
        print("   🔍 One-Class SVM...")
        ocsvm = OneClassSVM(nu=0.05, gamma='scale')
        ocsvm.fit(X_processed)

        # -1: anomali, 1: normal
        ocsvm_scores = ocsvm.decision_function(X_processed)
        ocsvm_labels = ocsvm.predict(X_processed)

        self.models['OneClassSVM'] = ocsvm
        self.results['OneClassSVM'] = {
            'anomaly_scores': ocsvm_scores,
            'anomaly_labels': ocsvm_labels,
            'anomaly_count': np.sum(ocsvm_labels == -1)
        }

        print("✅ Anomali tespiti modelleri eğitimi tamamlandı!")
        return X_processed, anomaly_labels

    def train_dimensionality_reduction(self, X):
        """Boyut indirgeme modellerini eğit"""
        print("🔍 Boyut indirgeme modelleri eğitiliyor...")

        # Veriyi ön işle
        X_processed = self.preprocessor.fit_transform(X)

        # Eğer sparse matris ise dense formata dönüştür
        if hasattr(X_processed, "toarray"):
            X_processed = X_processed.toarray()

        # 1. PCA
        print("   🔄 PCA...")
        pca = PCA(n_components=2, random_state=42)
        X_pca = pca.fit_transform(X_processed)

        self.models['PCA'] = pca
        self.results['PCA'] = {
            'explained_variance_ratio': pca.explained_variance_ratio_,
            'components': pca.components_,
            'transformed_data': X_pca
        }

        # 2. Feature Selection (SelectKBest)
        print("   🔄 Feature Selection...")
        selector = SelectKBest(f_regression, k=min(10, X_processed.shape[1]))
        X_selected = selector.fit_transform(X_processed, X['Fiyat'] if 'Fiyat' in X else None)

        self.models['SelectKBest'] = selector
        self.results['SelectKBest'] = {
            'scores': selector.scores_,
            'transformed_data': X_selected
        }

        print("✅ Boyut indirgeme modelleri eğitimi tamamlandı!")
        return X_pca

    def print_results(self):
        """Model sonuçlarını yazdır"""
        print("\n📊 MODEL SONUÇLARI")

        # Sınıflandırma sonuçları
        classification_models = ['LinearSVC', 'RandomForest', 'XGBoost', 'Stacking']
        print("\n🔍 Sınıflandırma Modelleri:")
        for model in classification_models:
            if model in self.results:
                print(f"   {model}: Doğruluk = {self.results[model]['accuracy']:.4f}")
                if 'best_params' in self.results[model]:
                    print(f"      En iyi parametreler: {self.results[model]['best_params']}")

        # Regresyon sonuçları
        regression_models = ['XGBRegressor', 'RFRegressor']
        print("\n📈 Regresyon Modelleri:")
        for model in regression_models:
            if model in self.results:
                print(f"   {model}: R² = {self.results[model]['r2']:.4f}, RMSE = {self.results[model]['rmse']:.2f}")

        # Kümeleme sonuçları
        clustering_models = ['KMeans', 'AgglomerativeClustering', 'DBSCAN', 'GaussianMixture']
        print("\n🔍 Kümeleme Modelleri:")
        for model in clustering_models:
            if model in self.results:
                print(f"   {model}: Silhouette = {self.results[model]['silhouette']:.4f}")
                if 'n_clusters' in self.results[model]:
                    print(f"      Küme sayısı: {self.results[model]['n_clusters']}")

        # Anomali tespiti sonuçları
        anomaly_models = ['IsolationForest', 'OneClassSVM']
        print("\n🔍 Anomali Tespiti Modelleri:")
        for model in anomaly_models:
            if model in self.results:
                print(f"   {model}: Anomali sayısı = {self.results[model]['anomaly_count']}")

    def save_models(self, output_dir='models'):
        """Modelleri kaydet"""
        print(f"\n💾 Modeller kaydediliyor: {output_dir}")

        # Klasör oluştur
        os.makedirs(output_dir, exist_ok=True)

        # Modelleri kaydet
        for model_name, model in self.models.items():
            try:
                model_path = os.path.join(output_dir, f"{model_name}.joblib")
                joblib.dump(model, model_path)
                print(f"   ✅ {model_name} kaydedildi: {model_path}")
            except Exception as e:
                print(f"   ❌ {model_name} kaydedilemedi: {e}")

    def load_models(self, input_dir='models'):
        """Modelleri yükle"""
        print(f"\n📂 Modeller yükleniyor: {input_dir}")

        # Klasör kontrolü
        if not os.path.exists(input_dir):
            print(f"   ❌ Klasör bulunamadı: {input_dir}")
            return False

        # Modelleri yükle
        for model_file in os.listdir(input_dir):
            if model_file.endswith('.joblib'):
                try:
                    model_name = os.path.splitext(model_file)[0]
                    model_path = os.path.join(input_dir, model_file)
                    self.models[model_name] = joblib.load(model_path)
                    print(f"   ✅ {model_name} yüklendi")
                except Exception as e:
                    print(f"   ❌ {model_file} yüklenemedi: {e}")

        return len(self.models) > 0


class GayrimenkulVisualizer:
    """Gayrimenkul veri ve model görselleştirme sınıfı"""

    def __init__(self, processor, models):
        self.processor = processor
        self.models = models
        self.df = processor.df

    def plot_data_overview(self):
        """Veri genel bakış grafikleri"""
        print("📊 Veri genel bakış grafikleri oluşturuluyor...")

        # Figür oluştur
        plt.figure(figsize=(15, 10))

        # 1. Fiyat dağılımı
        plt.subplot(2, 2, 1)
        sns.histplot(self.df['Fiyat'], kde=True)
        plt.title('Fiyat Dağılımı')
        plt.xlabel('Fiyat')
        plt.ylabel('Frekans')

        # 2. Alan dağılımı
        plt.subplot(2, 2, 2)
        sns.histplot(self.df['Net_Metrekare'], kde=True)
        plt.title('Alan Dağılımı')
        plt.xlabel('Alan (m²)')
        plt.ylabel('Frekans')

        # 3. Fiyat-Alan ilişkisi
        plt.subplot(2, 2, 3)
        sns.scatterplot(x='Net_Metrekare', y='Fiyat', data=self.df)
        plt.title('Fiyat-Alan İlişkisi')
        plt.xlabel('Alan (m²)')
        plt.ylabel('Fiyat')

        # 4. Oda sayısına göre fiyat
        plt.subplot(2, 2, 4)
        sns.boxplot(x='Oda_Sayısı', y='Fiyat', data=self.df)
        plt.title('Oda Sayısına Göre Fiyat')
        plt.xlabel('Oda Sayısı')
        plt.ylabel('Fiyat')

        plt.tight_layout()
        plt.savefig('data_overview.png')
        plt.close()

        print("✅ Veri genel bakış grafikleri kaydedildi: data_overview.png")

    def plot_correlation_matrix(self):
        """Korelasyon matrisi grafiği"""
        print("📊 Korelasyon matrisi grafiği oluşturuluyor...")

        # Sayısal sütunlar için korelasyon matrisi
        corr_matrix = self.df[self.processor.numeric_features + [self.processor.target_column]].corr()

        # Figür oluştur
        plt.figure(figsize=(12, 10))
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt='.2f', linewidths=0.5)
        plt.title('Korelasyon Matrisi')
        plt.tight_layout()
        plt.savefig('correlation_matrix.png')
        plt.close()

        print("✅ Korelasyon matrisi grafiği kaydedildi: correlation_matrix.png")

    def plot_classification_results(self, X_test, y_test):
        """Sınıflandırma sonuçları grafikleri"""
        print("📊 Sınıflandırma sonuçları grafikleri oluşturuluyor...")

        # Modelleri kontrol et
        classification_models = ['LinearSVC', 'RandomForest', 'XGBoost', 'Stacking']
        available_models = [m for m in classification_models if m in self.models.models]

        if not available_models:
            print("❌ Sınıflandırma modeli bulunamadı!")
            return

        # Figür oluştur
        plt.figure(figsize=(15, 10))

        # Doğruluk karşılaştırması
        accuracies = [self.models.results[model]['accuracy'] for model in available_models]

        plt.bar(available_models, accuracies)
        plt.title('Sınıflandırma Modelleri Doğruluk Karşılaştırması')
        plt.xlabel('Model')
        plt.ylabel('Doğruluk')
        plt.ylim(0, 1)

        # Değerleri göster
        for i, v in enumerate(accuracies):
            plt.text(i, v + 0.01, f'{v:.4f}', ha='center')

        plt.tight_layout()
        plt.savefig('classification_results.png')
        plt.close()

        print("✅ Sınıflandırma sonuçları grafiği kaydedildi: classification_results.png")

    def plot_regression_results(self, X_test, y_test, y_pred):
        """Regresyon sonuçları grafikleri"""
        print("📊 Regresyon sonuçları grafikleri oluşturuluyor...")

        # Modelleri kontrol et
        regression_models = ['XGBRegressor', 'RFRegressor']
        available_models = [m for m in regression_models if m in self.models.models]

        if not available_models:
            print("❌ Regresyon modeli bulunamadı!")
            return

        # Figür oluştur
        plt.figure(figsize=(15, 10))

        # 1. Gerçek vs Tahmin
        plt.subplot(2, 2, 1)
        plt.scatter(y_test, y_pred, alpha=0.5)
        plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
        plt.title('Gerçek vs Tahmin')
        plt.xlabel('Gerçek Değer')
        plt.ylabel('Tahmin')

        # 2. Hata Dağılımı
        plt.subplot(2, 2, 2)
        errors = y_test - y_pred
        sns.histplot(errors, kde=True)
        plt.title('Hata Dağılımı')
        plt.xlabel('Hata')
        plt.ylabel('Frekans')

        # 3. R² ve RMSE karşılaştırması
        plt.subplot(2, 2, 3)
        r2_scores = [self.models.results[model]['r2'] for model in available_models]
        plt.bar(available_models, r2_scores)
        plt.title('R² Karşılaştırması')
        plt.xlabel('Model')
        plt.ylabel('R²')

        plt.subplot(2, 2, 4)
        rmse_scores = [self.models.results[model]['rmse'] for model in available_models]
        plt.bar(available_models, rmse_scores)
        plt.title('RMSE Karşılaştırması')
        plt.xlabel('Model')
        plt.ylabel('RMSE')

        plt.tight_layout()
        plt.savefig('regression_results.png')
        plt.close()

        print("✅ Regresyon sonuçları grafiği kaydedildi: regression_results.png")

    def plot_clustering_results(self, X_processed):
        """Kümeleme sonuçları grafikleri"""
        print("📊 Kümeleme sonuçları grafikleri oluşturuluyor...")

        # Modelleri kontrol et
        clustering_models = ['KMeans', 'AgglomerativeClustering', 'DBSCAN', 'GaussianMixture']
        available_models = [m for m in clustering_models if m in self.models.models]

        if not available_models:
            print("❌ Kümeleme modeli bulunamadı!")
            return

        # PCA ile 2 boyuta indir
        pca = PCA(n_components=2, random_state=42)
        X_2d = pca.fit_transform(X_processed)

        # Figür oluştur
        plt.figure(figsize=(15, 10))

        # Her model için kümeleme sonuçlarını göster
        for i, model_name in enumerate(available_models):
            plt.subplot(2, 2, i+1)

            # Küme etiketleri
            labels = self.models.results[model_name]['labels']

            # Scatter plot
            plt.scatter(X_2d[:, 0], X_2d[:, 1], c=labels, cmap='viridis', alpha=0.5)
            plt.title(f'{model_name} Kümeleme Sonuçları')
            plt.xlabel('PCA 1')
            plt.ylabel('PCA 2')

            # Silhouette skoru
            silhouette = self.models.results[model_name]['silhouette']
            plt.text(0.05, 0.95, f'Silhouette: {silhouette:.4f}',
                     transform=plt.gca().transAxes, fontsize=10,
                     verticalalignment='top', bbox=dict(boxstyle='round', alpha=0.1))

        plt.tight_layout()
        plt.savefig('clustering_results.png')
        plt.close()

        print("✅ Kümeleme sonuçları grafiği kaydedildi: clustering_results.png")

    def plot_anomaly_detection(self, X_processed, anomaly_labels):
        """Anomali tespiti grafikleri"""
        print("📊 Anomali tespiti grafikleri oluşturuluyor...")

        # Modelleri kontrol et
        anomaly_models = ['IsolationForest', 'OneClassSVM']
        available_models = [m for m in anomaly_models if m in self.models.models]

        if not available_models:
            print("❌ Anomali tespiti modeli bulunamadı!")
            return

        # PCA ile 2 boyuta indir
        pca = PCA(n_components=2, random_state=42)
        X_2d = pca.fit_transform(X_processed)

        # Figür oluştur
        plt.figure(figsize=(15, 10))

        # Her model için anomali tespiti sonuçlarını göster
        for i, model_name in enumerate(available_models):
            plt.subplot(2, 2, i+1)

            # Anomali etiketleri (-1: anomali, 1: normal)
            labels = self.models.results[model_name]['anomaly_labels']

            # Normal ve anomali noktaları
            normal = labels == 1
            anomaly = labels == -1

            # Scatter plot
            plt.scatter(X_2d[normal, 0], X_2d[normal, 1], c='blue', alpha=0.5, label='Normal')
            plt.scatter(X_2d[anomaly, 0], X_2d[anomaly, 1], c='red', alpha=0.5, label='Anomali')

            plt.title(f'{model_name} Anomali Tespiti')
            plt.xlabel('PCA 1')
            plt.ylabel('PCA 2')
            plt.legend()

            # Anomali sayısı
            anomaly_count = self.models.results[model_name]['anomaly_count']
            plt.text(0.05, 0.95, f'Anomali Sayısı: {anomaly_count}',
                     transform=plt.gca().transAxes, fontsize=10,
                     verticalalignment='top', bbox=dict(boxstyle='round', alpha=0.1))

        plt.tight_layout()
        plt.savefig('anomaly_detection.png')
        plt.close()

        print("✅ Anomali tespiti grafiği kaydedildi: anomaly_detection.png")

    def plot_dimensionality_reduction(self, X_pca):
        """Boyut indirgeme grafikleri"""
        print("📊 Boyut indirgeme grafikleri oluşturuluyor...")

        # PCA sonuçları
        if 'PCA' not in self.models.results:
            print("❌ PCA sonuçları bulunamadı!")
            return

        # Figür oluştur
        plt.figure(figsize=(15, 10))

        # 1. PCA Scatter Plot
        plt.subplot(2, 2, 1)
        plt.scatter(X_pca[:, 0], X_pca[:, 1], alpha=0.5)
        plt.title('PCA: 2 Boyutlu Gösterim')
        plt.xlabel('PCA 1')
        plt.ylabel('PCA 2')

        # 2. Açıklanan varyans oranı
        plt.subplot(2, 2, 2)
        explained_variance = self.models.results['PCA']['explained_variance_ratio']
        plt.bar(range(1, len(explained_variance) + 1), explained_variance)
        plt.title('PCA: Açıklanan Varyans Oranı')
        plt.xlabel('Bileşen')
        plt.ylabel('Açıklanan Varyans Oranı')

        # 3. Kümülatif açıklanan varyans
        plt.subplot(2, 2, 3)
        cumulative_variance = np.cumsum(explained_variance)
        plt.plot(range(1, len(cumulative_variance) + 1), cumulative_variance, marker='o')
        plt.title('PCA: Kümülatif Açıklanan Varyans')
        plt.xlabel('Bileşen Sayısı')
        plt.ylabel('Kümülatif Açıklanan Varyans')
        plt.axhline(y=0.8, color='r', linestyle='--', label='80% Varyans')
        plt.legend()

        plt.tight_layout()
        plt.savefig('dimensionality_reduction.png')
        plt.close()

        print("✅ Boyut indirgeme grafiği kaydedildi: dimensionality_reduction.png")

    def plot_all(self, X_test=None, y_test=None, y_pred=None, X_processed=None, anomaly_labels=None, X_pca=None):
        """Tüm grafikleri oluştur"""
        print("\n📊 TÜM GRAFİKLER OLUŞTURULUYOR...")

        # Veri genel bakış
        self.plot_data_overview()

        # Korelasyon matrisi
        self.plot_correlation_matrix()

        # Sınıflandırma sonuçları
        if X_test is not None and y_test is not None:
            self.plot_classification_results(X_test, y_test)

        # Regresyon sonuçları
        if X_test is not None and y_test is not None and y_pred is not None:
            self.plot_regression_results(X_test, y_test, y_pred)

        # Kümeleme sonuçları
        if X_processed is not None:
            self.plot_clustering_results(X_processed)

        # Anomali tespiti
        if X_processed is not None and anomaly_labels is not None:
            self.plot_anomaly_detection(X_processed, anomaly_labels)

        # Boyut indirgeme
        if X_pca is not None:
            self.plot_dimensionality_reduction(X_pca)

        print("✅ Tüm grafikler oluşturuldu!")


def run_full_analysis(data_path, output_dir='results'):
    """Tam analiz çalıştır"""
    start_time = time.time()
    print("\n🚀 GAYRİMENKUL KARAR DESTEK SİSTEMİ - TAM ANALİZ")
    print("=" * 70)

    # Çıktı klasörü oluştur
    os.makedirs(output_dir, exist_ok=True)

    # 1. Veri İşleme
    print("\n📊 1. VERİ İŞLEME")
    processor = GayrimenkulDataProcessor()

    # Veri yükleme
    df = processor.load_data(data_path)

    # Veri temizleme
    df = processor.clean_data()

    # Özellik mühendisliği
    df = processor.feature_engineering()

    # Korelasyon analizi
    corr_matrix = processor.analyze_correlations()

    # Kategorik değişken analizi
    cat_impact = processor.analyze_categorical_impact()

    # Özellikleri hazırla
    X, y = processor.prepare_features()

    # Sınıflandırma için hedef değişkeni hazırla
    y_class = processor.prepare_classification_target(y)

    # Ön işleme pipeline'ı oluştur
    preprocessor = processor.create_preprocessor()

    # 2. Makine Öğrenmesi Modelleri
    print("\n🤖 2. MAKİNE ÖĞRENMESİ MODELLERİ")
    ml_models = GayrimenkulMLModels(preprocessor)

    # Sınıflandırma modelleri
    X_test_class, y_test_class = ml_models.train_classification_models(X, y_class)

    # Regresyon modeli
    X_test_reg, y_test_reg, y_pred_reg = ml_models.train_regression_model(X, y)

    # Kümeleme modelleri
    X_processed = ml_models.train_clustering_models(X)

    # Anomali tespiti
    X_processed_anomaly, anomaly_labels = ml_models.train_anomaly_detection(X)

    # Boyut indirgeme
    X_pca = ml_models.train_dimensionality_reduction(X)

    # Sonuçları yazdır
    ml_models.print_results()

    # Modelleri kaydet
    ml_models.save_models(os.path.join(output_dir, 'models'))

    # 3. Görselleştirme
    print("\n📊 3. GÖRSELLEŞTİRME")
    visualizer = GayrimenkulVisualizer(processor, ml_models)
    visualizer.plot_all(
        X_test=X_test_class,
        y_test=y_test_class,
        y_pred=y_pred_reg,
        X_processed=X_processed,
        anomaly_labels=anomaly_labels,
        X_pca=X_pca
    )

    # 4. Sonuç
    elapsed_time = time.time() - start_time
    print("\n✅ ANALİZ TAMAMLANDI!")
    print(f"Toplam süre: {elapsed_time:.2f} saniye")
    print(f"Sonuçlar '{output_dir}' klasörüne kaydedildi.")
    print("=" * 70)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Gayrimenkul Karar Destek Sistemi - Tam Analiz')
    parser.add_argument('--data', type=str, default='data/gayrimenkul_verileri.csv', help='Veri dosyası yolu')
    parser.add_argument('--output', type=str, default='results', help='Çıktı klasörü')
    parser.add_argument('--limit', type=int, default=None, help='Veri limiti (satır sayısı)')

    args = parser.parse_args()

    # Tam analizi çalıştır
    run_full_analysis(args.data, args.output)

