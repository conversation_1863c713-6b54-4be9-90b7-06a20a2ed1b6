#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEZ İÇİN KAPSAMLI GAYRİMENKUL VERİ MADENCİLİĞİ VE MAKİNE ÖĞRENMESİ ANALİZİ
===========================================================================

Bu dosya tez için tüm veri madenciliği ve makine öğrenmesi yöntemlerini
aşama aşama uygular, her adımda:
- Neden bu yöntem kullanıldığını açıklar
- Çıktıları detaylı gösterir
- Görselleştirmeler oluşturur
- Sonuçları yorumlar
- Karşılaştırmalar yapar

Kullanılan Yöntemler:
1. Veri Ön İşleme ve Temizleme
2. Keşifsel Veri Analizi (EDA)
3. Özellik Mühendisliği
4. Aykırı Değer Tespiti
5. <PERSON><PERSON><PERSON> (Isolation Forest, One-Class SVM)
6. K<PERSON><PERSON><PERSON>e Algoritmaları (K-Means, Hierarchical, DBSCAN, GMM)
7. Sınıflandırma Modelleri (Random Forest, SVM, XGBoost, Stacking)
8. Regresyon Modelleri (Linear, Random Forest, XGBoost)
9. Boyut İndirgeme (PCA)
10. Özellik Seçimi
11. Model Değerlendirme ve Karşılaştırma
12. Yatırım Uygunluk Analizi
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Makine Öğrenmesi Kütüphaneleri
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline

# Sınıflandırma Modelleri
from sklearn.svm import LinearSVC
from sklearn.ensemble import RandomForestClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression

# Regresyon Modelleri
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression

# Kümeleme Algoritmaları
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN
from sklearn.mixture import GaussianMixture

# Anomali Tespiti
from sklearn.ensemble import IsolationForest
from sklearn.svm import OneClassSVM

# Boyut İndirgeme ve Özellik Seçimi
from sklearn.decomposition import PCA
from sklearn.feature_selection import SelectKBest, f_regression, RFE

# Metrikler
from sklearn.metrics import (
    accuracy_score, classification_report, confusion_matrix,
    mean_squared_error, r2_score, mean_absolute_error,
    silhouette_score, adjusted_rand_score
)

# XGBoost (opsiyonel)
try:
    from xgboost import XGBClassifier, XGBRegressor
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

# Grafik ayarları
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

class TezKapsamliAnaliz:
    """Tez için kapsamlı veri madenciliği ve makine öğrenmesi analizi"""

    def __init__(self, data_path="data/home_price.csv"):
        self.data_path = data_path
        self.df = None
        self.df_original = None
        self.numeric_features = []
        self.categorical_features = []
        self.target_column = 'Fiyat'
        self.models = {}
        self.results = {}
        self.preprocessor = None
        self.analysis_steps = []

        print("📚 TEZ İÇİN KAPSAMLI GAYRİMENKUL ANALİZİ")
        print("=" * 60)
        print("Bu analiz tez için tüm yöntemleri aşama aşama uygular")
        print("Her adımda detaylı açıklama, görsel ve sonuç verir")
        print("=" * 60)

    def add_analysis_step(self, step_name, description, method_reason, results, conclusion):
        """Analiz adımını kaydet"""
        self.analysis_steps.append({
            'step': step_name,
            'description': description,
            'method_reason': method_reason,
            'results': results,
            'conclusion': conclusion
        })

    def step1_data_loading_and_exploration(self):
        """ADIM 1: Veri Yükleme ve Keşifsel Veri Analizi"""
        print("\n" + "="*80)
        print("📊 ADIM 1: VERİ YÜKLEME VE KEŞİFSEL VERİ ANALİZİ (EDA)")
        print("="*80)

        # Veri yükleme
        print("\n🔍 1.1 VERİ YÜKLEME")
        print("-" * 30)

        try:
            self.df = pd.read_csv(self.data_path)
            self.df_original = self.df.copy()
            print(f"✅ Veri başarıyla yüklendi")
            print(f"📊 Boyut: {self.df.shape[0]:,} satır × {self.df.shape[1]} sütun")

            # Temel bilgiler
            print(f"\n📋 Sütun Bilgileri:")
            for i, col in enumerate(self.df.columns, 1):
                dtype = self.df[col].dtype
                null_count = self.df[col].isnull().sum()
                null_pct = (null_count / len(self.df)) * 100
                print(f"   {i:2d}. {col:<20} | {str(dtype):<10} | Eksik: {null_count:>5} ({null_pct:>5.1f}%)")

        except Exception as e:
            print(f"❌ Veri yükleme hatası: {e}")
            return False

        # Keşifsel Veri Analizi
        print(f"\n🔍 1.2 KEŞİFSEL VERİ ANALİZİ")
        print("-" * 35)

        # Sayısal değişkenler için istatistikler
        numeric_cols = self.df.select_dtypes(include=[np.number]).columns
        print(f"\n📊 Sayısal Değişkenler İstatistikleri:")
        print(self.df[numeric_cols].describe())

        # Kategorik değişkenler için frekanslar
        categorical_cols = self.df.select_dtypes(include=['object']).columns
        print(f"\n📋 Kategorik Değişkenler:")
        for col in categorical_cols:
            unique_count = self.df[col].nunique()
            print(f"   • {col}: {unique_count} benzersiz değer")
            if unique_count <= 10:
                print(f"     Değerler: {list(self.df[col].value_counts().head().index)}")

        # Görselleştirmeler
        self._create_eda_visualizations()

        # Sonuçları kaydet
        results = {
            'total_records': len(self.df),
            'total_features': len(self.df.columns),
            'numeric_features': len(numeric_cols),
            'categorical_features': len(categorical_cols),
            'missing_data_percentage': (self.df.isnull().sum().sum() / (len(self.df) * len(self.df.columns))) * 100
        }

        self.add_analysis_step(
            step_name="Veri Yükleme ve EDA",
            description="Gayrimenkul veri setinin yüklenmesi ve temel özelliklerinin incelenmesi",
            method_reason="Veri setini anlamak, kalitesini değerlendirmek ve sonraki adımları planlamak için EDA yapıldı",
            results=results,
            conclusion=f"Toplam {len(self.df):,} gayrimenkul verisi analiz edilecek. Veri setinde {len(categorical_cols)} kategorik ve {len(numeric_cols)} sayısal özellik bulunuyor."
        )

        return True

    def _create_eda_visualizations(self):
        """EDA görselleştirmeleri oluştur"""
        print("\n📈 Görselleştirmeler oluşturuluyor...")

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Keşifsel Veri Analizi (EDA)', fontsize=16, fontweight='bold')

        # 1. Fiyat dağılımı
        axes[0,0].hist(self.df['Fiyat'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0,0].set_title('Fiyat Dağılımı')
        axes[0,0].set_xlabel('Fiyat (TL)')
        axes[0,0].set_ylabel('Frekans')
        axes[0,0].ticklabel_format(style='plain', axis='x')

        # 2. Alan dağılımı
        axes[0,1].hist(self.df['Net_Metrekare'], bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0,1].set_title('Net Metrekare Dağılımı')
        axes[0,1].set_xlabel('Net Metrekare (m²)')
        axes[0,1].set_ylabel('Frekans')

        # 3. Fiyat-Alan scatter plot
        axes[0,2].scatter(self.df['Net_Metrekare'], self.df['Fiyat'], alpha=0.5, s=10)
        axes[0,2].set_title('Fiyat-Alan İlişkisi')
        axes[0,2].set_xlabel('Net Metrekare (m²)')
        axes[0,2].set_ylabel('Fiyat (TL)')
        axes[0,2].ticklabel_format(style='plain', axis='y')

        # 4. Şehir dağılımı
        city_counts = self.df['Şehir'].value_counts().head(10)
        axes[1,0].bar(range(len(city_counts)), city_counts.values, color='orange', alpha=0.7)
        axes[1,0].set_title('En Çok Gayrimenkul Bulunan Şehirler')
        axes[1,0].set_xlabel('Şehirler')
        axes[1,0].set_ylabel('Gayrimenkul Sayısı')
        axes[1,0].set_xticks(range(len(city_counts)))
        axes[1,0].set_xticklabels(city_counts.index, rotation=45)

        # 5. Oda sayısı dağılımı
        if 'Oda_Sayısı' in self.df.columns:
            room_counts = self.df['Oda_Sayısı'].value_counts().sort_index()
            axes[1,1].bar(room_counts.index, room_counts.values, color='purple', alpha=0.7)
            axes[1,1].set_title('Oda Sayısı Dağılımı')
            axes[1,1].set_xlabel('Oda Sayısı')
            axes[1,1].set_ylabel('Gayrimenkul Sayısı')

        # 6. Eksik veri analizi
        missing_data = self.df.isnull().sum()
        missing_data = missing_data[missing_data > 0].sort_values(ascending=True)
        if len(missing_data) > 0:
            axes[1,2].barh(range(len(missing_data)), missing_data.values, color='red', alpha=0.7)
            axes[1,2].set_title('Eksik Veri Analizi')
            axes[1,2].set_xlabel('Eksik Değer Sayısı')
            axes[1,2].set_yticks(range(len(missing_data)))
            axes[1,2].set_yticklabels(missing_data.index)
        else:
            axes[1,2].text(0.5, 0.5, 'Eksik Veri Yok', ha='center', va='center', transform=axes[1,2].transAxes)
            axes[1,2].set_title('Eksik Veri Analizi')

        plt.tight_layout()
        plt.savefig('tez_eda_grafikleri.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ EDA grafikleri 'tez_eda_grafikleri.png' olarak kaydedildi")

    def step2_data_preprocessing(self):
        """ADIM 2: Veri Ön İşleme ve Temizleme"""
        print("\n" + "="*80)
        print("🧹 ADIM 2: VERİ ÖN İŞLEME VE TEMİZLEME")
        print("="*80)

        print("\n📋 2.1 NEDEN VERİ ÖN İŞLEME GEREKLİ?")
        print("-" * 40)
        print("• Ham veri genellikle eksik değerler, aykırı değerler ve tutarsızlıklar içerir")
        print("• Makine öğrenmesi algoritmaları temiz ve düzenli veri gerektirir")
        print("• Veri kalitesi model performansını doğrudan etkiler")
        print("• Eksik değerler algoritmaların çalışmasını engelleyebilir")

        initial_count = len(self.df)

        # Eksik değer analizi
        print(f"\n🔍 2.2 EKSİK DEĞER ANALİZİ")
        print("-" * 30)
        missing_data = self.df.isnull().sum()
        missing_data = missing_data[missing_data > 0].sort_values(ascending=False)

        if len(missing_data) > 0:
            print("⚠️ Eksik değerler tespit edildi:")
            for col, count in missing_data.items():
                percentage = (count / len(self.df)) * 100
                print(f"   • {col}: {count:,} ({percentage:.1f}%)")
        else:
            print("✅ Eksik değer bulunamadı")

        # Geçersiz değerleri temizle
        print(f"\n🗑️ 2.3 GEÇERSİZ DEĞER TEMİZLEME")
        print("-" * 35)
        print("Temizleme kriterleri:")
        print("• Fiyat ≤ 0 olan kayıtlar")
        print("• Net metrekare ≤ 0 olan kayıtlar")
        print("• Aşırı yüksek değerler (99.9 percentile üstü)")

        # Fiyat = 0 olan kayıtları kaldır
        before_price = len(self.df)
        self.df = self.df[self.df['Fiyat'] > 0]
        after_price = len(self.df)
        print(f"   • Geçersiz fiyat: {before_price - after_price:,} kayıt kaldırıldı")

        # Net metrekare = 0 olan kayıtları kaldır
        before_area = len(self.df)
        self.df = self.df[self.df['Net_Metrekare'] > 0]
        after_area = len(self.df)
        print(f"   • Geçersiz alan: {before_area - after_area:,} kayıt kaldırıldı")

        # Aşırı yüksek değerleri filtrele
        price_threshold = self.df['Fiyat'].quantile(0.999)
        area_threshold = self.df['Net_Metrekare'].quantile(0.999)

        before_outlier = len(self.df)
        self.df = self.df[
            (self.df['Fiyat'] <= price_threshold) &
            (self.df['Net_Metrekare'] <= area_threshold)
        ]
        after_outlier = len(self.df)
        print(f"   • Aşırı değerler: {before_outlier - after_outlier:,} kayıt kaldırıldı")

        final_count = len(self.df)
        removed_count = initial_count - final_count

        print(f"\n📊 TEMİZLEME SONUÇLARI:")
        print(f"   • Başlangıç: {initial_count:,} kayıt")
        print(f"   • Temizlenen: {removed_count:,} kayıt ({removed_count/initial_count*100:.1f}%)")
        print(f"   • Kalan: {final_count:,} kayıt ({final_count/initial_count*100:.1f}%)")

        # Aykırı değer tespiti (IQR yöntemi)
        self._detect_outliers_iqr()

        # Sonuçları kaydet
        results = {
            'initial_records': initial_count,
            'final_records': final_count,
            'removed_records': removed_count,
            'removal_percentage': (removed_count/initial_count)*100,
            'data_quality_score': (final_count/initial_count)*100
        }

        self.add_analysis_step(
            step_name="Veri Ön İşleme",
            description="Geçersiz değerlerin temizlenmesi ve veri kalitesinin artırılması",
            method_reason="Makine öğrenmesi algoritmalarının doğru çalışması için temiz veri gereklidir. Geçersiz değerler model performansını olumsuz etkiler.",
            results=results,
            conclusion=f"Veri temizleme sonucunda {removed_count:,} geçersiz kayıt kaldırıldı. Veri kalitesi %{results['data_quality_score']:.1f} seviyesinde."
        )

        return True

    def _detect_outliers_iqr(self):
        """IQR yöntemi ile aykırı değer tespiti"""
        print(f"\n🔍 2.4 AYKIRI DEĞER TESPİTİ (IQR YÖNTEMİ)")
        print("-" * 45)
        print("IQR (Interquartile Range) Yöntemi:")
        print("• Q1: 1. çeyrek (25. percentile)")
        print("• Q3: 3. çeyrek (75. percentile)")
        print("• IQR = Q3 - Q1")
        print("• Aykırı değer: Q1 - 1.5×IQR > değer > Q3 + 1.5×IQR")

        numeric_cols = ['Net_Metrekare', 'Brüt_Metrekare', 'Oda_Sayısı', 'Fiyat', 'Banyo_Sayısı']
        outlier_summary = {}

        for col in numeric_cols:
            if col in self.df.columns:
                Q1 = self.df[col].quantile(0.25)
                Q3 = self.df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers = self.df[(self.df[col] < lower_bound) | (self.df[col] > upper_bound)]
                outlier_count = len(outliers)

                if outlier_count > 0:
                    percentage = (outlier_count / len(self.df)) * 100
                    print(f"   • {col}: {outlier_count:,} aykırı değer ({percentage:.1f}%)")
                    outlier_summary[col] = {'count': outlier_count, 'percentage': percentage}
                else:
                    print(f"   • {col}: Aykırı değer yok")
                    outlier_summary[col] = {'count': 0, 'percentage': 0}

        return outlier_summary

    def step3_feature_engineering(self):
        """ADIM 3: Özellik Mühendisliği"""
        print("\n" + "="*80)
        print("🔧 ADIM 3: ÖZELLİK MÜHENDİSLİĞİ")
        print("="*80)

        print("\n📋 3.1 NEDEN ÖZELLİK MÜHENDİSLİĞİ?")
        print("-" * 40)
        print("• Ham özellikler her zaman en iyi performansı vermez")
        print("• Yeni özellikler gizli kalıpları ortaya çıkarabilir")
        print("• Özellik kombinasyonları daha güçlü tahminler sağlar")
        print("• Domain bilgisi ile anlamlı özellikler oluşturulabilir")

        # Mevcut özellikleri kategorize et
        numeric_cols = ['Net_Metrekare', 'Brüt_Metrekare', 'Oda_Sayısı', 'Binanın_Kat_Sayısı', 'Banyo_Sayısı']
        categorical_cols = ['Şehir', 'Eşya_Durumu', 'Binanın_Yaşı', 'Isıtma_Tipi', 'Bulunduğu_Kat',
                           'Kullanım_Durumu', 'Yatırıma_Uygunluk', 'Takas', 'Tapu_Durumu']

        self.numeric_features = [col for col in numeric_cols if col in self.df.columns]
        self.categorical_features = [col for col in categorical_cols if col in self.df.columns]

        print(f"\n📊 MEVCUT ÖZELLİKLER:")
        print(f"   • Sayısal: {len(self.numeric_features)} özellik")
        print(f"   • Kategorik: {len(self.categorical_features)} özellik")

        # Yeni özellikler oluştur
        print(f"\n🆕 3.2 YENİ ÖZELLİK OLUŞTURMA")
        print("-" * 35)

        new_features_created = 0

        # 1. Fiyat/m² oranı
        print("1. Fiyat/m² Oranı:")
        print("   • Gayrimenkul değerlendirmesinde en önemli metrik")
        print("   • Farklı büyüklükteki gayrimenkulleri karşılaştırma imkanı")
        self.df['Fiyat_Per_M2'] = self.df['Fiyat'] / self.df['Net_Metrekare']
        self.numeric_features.append('Fiyat_Per_M2')
        new_features_created += 1
        print(f"   ✅ Oluşturuldu: Ortalama {self.df['Fiyat_Per_M2'].mean():,.0f} TL/m²")

        # 2. Oda başına alan
        if 'Oda_Sayısı' in self.df.columns:
            print("\n2. Oda Başına Alan:")
            print("   • Yaşam konforunu gösteren önemli metrik")
            print("   • Oda büyüklüğü hakkında bilgi verir")
            self.df['Alan_Per_Oda'] = self.df['Net_Metrekare'] / (self.df['Oda_Sayısı'] + 1)
            self.numeric_features.append('Alan_Per_Oda')
            new_features_created += 1
            print(f"   ✅ Oluşturuldu: Ortalama {self.df['Alan_Per_Oda'].mean():.1f} m²/oda")

        # 3. Brüt/Net alan oranı
        if 'Brüt_Metrekare' in self.df.columns:
            print("\n3. Brüt/Net Alan Oranı:")
            print("   • Binanın verimlilik oranını gösterir")
            print("   • Ortak alanların büyüklüğü hakkında bilgi")
            self.df['Brut_Net_Oran'] = self.df['Brüt_Metrekare'] / self.df['Net_Metrekare']
            self.numeric_features.append('Brut_Net_Oran')
            new_features_created += 1
            print(f"   ✅ Oluşturuldu: Ortalama oran {self.df['Brut_Net_Oran'].mean():.2f}")

        # 4. Banyo/Oda oranı
        if 'Banyo_Sayısı' in self.df.columns and 'Oda_Sayısı' in self.df.columns:
            print("\n4. Banyo/Oda Oranı:")
            print("   • Konfor seviyesini gösteren metrik")
            print("   • Lüks seviyesi hakkında bilgi")
            self.df['Banyo_Oda_Oran'] = self.df['Banyo_Sayısı'] / (self.df['Oda_Sayısı'] + 1)
            self.numeric_features.append('Banyo_Oda_Oran')
            new_features_created += 1
            print(f"   ✅ Oluşturuldu: Ortalama oran {self.df['Banyo_Oda_Oran'].mean():.2f}")

        # 5. Şehir büyüklük kategorisi
        if 'Şehir' in self.df.columns:
            print("\n5. Şehir Büyüklük Kategorisi:")
            print("   • Şehrin gayrimenkul piyasasındaki büyüklüğü")
            print("   • Likidite ve yatırım potansiyeli göstergesi")
            city_counts = self.df['Şehir'].value_counts()
            self.df['Sehir_Buyukluk'] = self.df['Şehir'].map(city_counts)
            self.numeric_features.append('Sehir_Buyukluk')
            new_features_created += 1
            print(f"   ✅ Oluşturuldu: En büyük piyasa {city_counts.index[0]} ({city_counts.iloc[0]:,} gayrimenkul)")

        # 6. Yeni bina kategorisi
        if 'Binanın_Yaşı' in self.df.columns:
            print("\n6. Yeni Bina Kategorisi:")
            print("   • Yeni binaların ayrı kategoride değerlendirilmesi")
            print("   • Yapı kalitesi ve modern özellikler göstergesi")
            self.df['Yeni_Bina'] = (self.df['Binanın_Yaşı'].str.contains('0 \(Yeni\)', na=False)).astype(int)
            self.numeric_features.append('Yeni_Bina')
            new_features_created += 1
            yeni_bina_sayisi = self.df['Yeni_Bina'].sum()
            print(f"   ✅ Oluşturuldu: {yeni_bina_sayisi:,} yeni bina tespit edildi")

        # 7. Lüks kategori
        print("\n7. Lüks Kategori:")
        print("   • Alan ve fiyat bazlı lüks sınıflandırması")
        print("   • Üst segment gayrimenkul tespiti")
        area_threshold = self.df['Net_Metrekare'].quantile(0.8)
        price_threshold = self.df['Fiyat'].quantile(0.8)
        self.df['Luks_Kategori'] = ((self.df['Net_Metrekare'] >= area_threshold) &
                                   (self.df['Fiyat'] >= price_threshold)).astype(int)
        self.numeric_features.append('Luks_Kategori')
        new_features_created += 1
        luks_sayisi = self.df['Luks_Kategori'].sum()
        print(f"   ✅ Oluşturuldu: {luks_sayisi:,} lüks gayrimenkul tespit edildi")

        print(f"\n📊 ÖZELLİK MÜHENDİSLİĞİ SONUÇLARI:")
        print(f"   • Oluşturulan yeni özellik: {new_features_created}")
        print(f"   • Toplam sayısal özellik: {len(self.numeric_features)}")
        print(f"   • Toplam kategorik özellik: {len(self.categorical_features)}")
        print(f"   • Genel toplam özellik: {len(self.numeric_features) + len(self.categorical_features)}")

        # Sonuçları kaydet
        results = {
            'new_features_created': new_features_created,
            'total_numeric_features': len(self.numeric_features),
            'total_categorical_features': len(self.categorical_features),
            'total_features': len(self.numeric_features) + len(self.categorical_features)
        }

        self.add_analysis_step(
            step_name="Özellik Mühendisliği",
            description="Domain bilgisi kullanarak yeni anlamlı özellikler oluşturma",
            method_reason="Ham özellikler tek başına yeterli bilgi vermeyebilir. Özellik kombinasyonları ve oranlar daha güçlü tahmin gücü sağlar.",
            results=results,
            conclusion=f"{new_features_created} yeni özellik oluşturuldu. Bu özellikler gayrimenkul değerlendirmesinde kritik metrikleri temsil ediyor."
        )

        return True

    def step4_anomaly_detection(self):
        """ADIM 4: Anomali Tespiti"""
        print("\n" + "="*80)
        print("🚨 ADIM 4: ANOMALİ TESPİTİ")
        print("="*80)

        print("\n📋 4.1 NEDEN ANOMALİ TESPİTİ?")
        print("-" * 35)
        print("• Anormal fiyatlı gayrimenkulleri tespit etmek")
        print("• Veri kalitesini artırmak")
        print("• Sahte ilanları veya hatalı girişleri bulmak")
        print("• Piyasa dışı fiyatlandırmaları belirlemek")

        # Veri hazırlama
        X_numeric = self.df[self.numeric_features].fillna(self.df[self.numeric_features].median())

        print(f"\n🔍 4.2 ISOLATION FOREST YÖNTEMİ")
        print("-" * 40)
        print("Isolation Forest Algoritması:")
        print("• Ensemble tabanlı anomali tespit yöntemi")
        print("• Normal veriler daha zor izole edilir")
        print("• Anormal veriler daha kolay izole edilir")
        print("• Contamination parametresi: %10 (varsayılan)")

        # Isolation Forest
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        anomaly_labels_iso = iso_forest.fit_predict(X_numeric)
        anomaly_count_iso = np.sum(anomaly_labels_iso == -1)
        anomaly_percentage_iso = (anomaly_count_iso / len(self.df)) * 100

        print(f"✅ Sonuçlar:")
        print(f"   • Tespit edilen anomali: {anomaly_count_iso:,} kayıt")
        print(f"   • Anomali oranı: %{anomaly_percentage_iso:.1f}")
        print(f"   • Normal kayıt: {len(self.df) - anomaly_count_iso:,}")

        # One-Class SVM
        print(f"\n🔍 4.3 ONE-CLASS SVM YÖNTEMİ")
        print("-" * 35)
        print("One-Class SVM Algoritması:")
        print("• Support Vector Machine tabanlı")
        print("• Sadece normal verilerle eğitilir")
        print("• Karar sınırı oluşturarak anomali tespit eder")
        print("• Nu parametresi: 0.1 (beklenen anomali oranı)")

        try:
            # Veriyi ölçeklendir (SVM için gerekli)
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_numeric)

            oc_svm = OneClassSVM(nu=0.1, kernel='rbf', gamma='scale')
            anomaly_labels_svm = oc_svm.fit_predict(X_scaled)
            anomaly_count_svm = np.sum(anomaly_labels_svm == -1)
            anomaly_percentage_svm = (anomaly_count_svm / len(self.df)) * 100

            print(f"✅ Sonuçlar:")
            print(f"   • Tespit edilen anomali: {anomaly_count_svm:,} kayıt")
            print(f"   • Anomali oranı: %{anomaly_percentage_svm:.1f}")
            print(f"   • Normal kayıt: {len(self.df) - anomaly_count_svm:,}")

        except Exception as e:
            print(f"   ⚠️ One-Class SVM hatası: {e}")
            anomaly_labels_svm = np.ones(len(self.df))  # Hata durumunda tüm veriler normal
            anomaly_count_svm = 0
            anomaly_percentage_svm = 0

        # Sonuçları DataFrame'e ekle
        self.df['Anomali_ISO'] = anomaly_labels_iso
        self.df['Anomali_SVM'] = anomaly_labels_svm
        self.df['Anomali_Skoru_ISO'] = iso_forest.decision_function(X_numeric)

        # Ortak anomaliler
        common_anomalies = np.sum((anomaly_labels_iso == -1) & (anomaly_labels_svm == -1))

        print(f"\n📊 4.4 KARŞILAŞTIRMA VE DEĞERLENDİRME")
        print("-" * 45)
        print(f"• Isolation Forest anomali: {anomaly_count_iso:,} (%{anomaly_percentage_iso:.1f})")
        print(f"• One-Class SVM anomali: {anomaly_count_svm:,} (%{anomaly_percentage_svm:.1f})")
        print(f"• Her iki yöntemde ortak: {common_anomalies:,}")
        print(f"• Toplam benzersiz anomali: {len(self.df[(self.df['Anomali_ISO'] == -1) | (self.df['Anomali_SVM'] == -1)]):,}")

        # Anomali örnekleri
        iso_anomalies = self.df[self.df['Anomali_ISO'] == -1]
        if len(iso_anomalies) > 0:
            print(f"\n🔍 ANOMALİ ÖRNEKLERİ (Isolation Forest):")
            print("En yüksek fiyatlı anomaliler:")
            top_anomalies = iso_anomalies.nlargest(3, 'Fiyat')[['Şehir', 'Net_Metrekare', 'Fiyat', 'Fiyat_Per_M2']]
            for idx, row in top_anomalies.iterrows():
                print(f"   • {row['Şehir']}: {row['Net_Metrekare']:.0f}m², {row['Fiyat']:,.0f}TL ({row['Fiyat_Per_M2']:,.0f}TL/m²)")

        # Görselleştirme
        self._create_anomaly_visualizations(anomaly_labels_iso, anomaly_labels_svm)

        # Sonuçları kaydet
        results = {
            'isolation_forest_anomalies': anomaly_count_iso,
            'isolation_forest_percentage': anomaly_percentage_iso,
            'one_class_svm_anomalies': anomaly_count_svm,
            'one_class_svm_percentage': anomaly_percentage_svm,
            'common_anomalies': common_anomalies,
            'total_unique_anomalies': len(self.df[(self.df['Anomali_ISO'] == -1) | (self.df['Anomali_SVM'] == -1)])
        }

        self.add_analysis_step(
            step_name="Anomali Tespiti",
            description="Isolation Forest ve One-Class SVM ile anormal gayrimenkul fiyatlarının tespiti",
            method_reason="Anormal fiyatlı gayrimenkulleri tespit ederek veri kalitesini artırmak ve piyasa dışı fiyatlandırmaları belirlemek için kullanıldı.",
            results=results,
            conclusion=f"İki farklı yöntemle anomali tespiti yapıldı. Isolation Forest %{anomaly_percentage_iso:.1f}, One-Class SVM %{anomaly_percentage_svm:.1f} anomali tespit etti."
        )

        return True

    def _create_anomaly_visualizations(self, iso_labels, svm_labels):
        """Anomali tespiti görselleştirmeleri"""
        print("\n📈 Anomali tespiti grafikleri oluşturuluyor...")

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Anomali Tespiti Sonuçları', fontsize=16, fontweight='bold')

        # 1. Isolation Forest sonuçları
        normal_iso = self.df[iso_labels == 1]
        anomaly_iso = self.df[iso_labels == -1]

        axes[0,0].scatter(normal_iso['Net_Metrekare'], normal_iso['Fiyat'],
                         c='blue', alpha=0.6, s=10, label=f'Normal ({len(normal_iso):,})')
        axes[0,0].scatter(anomaly_iso['Net_Metrekare'], anomaly_iso['Fiyat'],
                         c='red', alpha=0.8, s=20, label=f'Anomali ({len(anomaly_iso):,})')
        axes[0,0].set_title('Isolation Forest Anomali Tespiti')
        axes[0,0].set_xlabel('Net Metrekare (m²)')
        axes[0,0].set_ylabel('Fiyat (TL)')
        axes[0,0].legend()
        axes[0,0].ticklabel_format(style='plain', axis='y')

        # 2. One-Class SVM sonuçları
        normal_svm = self.df[svm_labels == 1]
        anomaly_svm = self.df[svm_labels == -1]

        axes[0,1].scatter(normal_svm['Net_Metrekare'], normal_svm['Fiyat'],
                         c='green', alpha=0.6, s=10, label=f'Normal ({len(normal_svm):,})')
        axes[0,1].scatter(anomaly_svm['Net_Metrekare'], anomaly_svm['Fiyat'],
                         c='orange', alpha=0.8, s=20, label=f'Anomali ({len(anomaly_svm):,})')
        axes[0,1].set_title('One-Class SVM Anomali Tespiti')
        axes[0,1].set_xlabel('Net Metrekare (m²)')
        axes[0,1].set_ylabel('Fiyat (TL)')
        axes[0,1].legend()
        axes[0,1].ticklabel_format(style='plain', axis='y')

        # 3. Anomali skorları dağılımı
        axes[1,0].hist(self.df['Anomali_Skoru_ISO'], bins=50, alpha=0.7, color='purple')
        axes[1,0].axvline(x=0, color='red', linestyle='--', label='Anomali Eşiği')
        axes[1,0].set_title('Isolation Forest Anomali Skorları')
        axes[1,0].set_xlabel('Anomali Skoru')
        axes[1,0].set_ylabel('Frekans')
        axes[1,0].legend()

        # 4. Fiyat/m² bazında anomaliler
        axes[1,1].scatter(normal_iso['Fiyat_Per_M2'], normal_iso['Fiyat'],
                         c='blue', alpha=0.6, s=10, label='Normal')
        axes[1,1].scatter(anomaly_iso['Fiyat_Per_M2'], anomaly_iso['Fiyat'],
                         c='red', alpha=0.8, s=20, label='Anomali')
        axes[1,1].set_title('Fiyat/m² Bazında Anomaliler')
        axes[1,1].set_xlabel('Fiyat/m² (TL)')
        axes[1,1].set_ylabel('Fiyat (TL)')
        axes[1,1].legend()
        axes[1,1].ticklabel_format(style='plain')

        plt.tight_layout()
        plt.savefig('tez_anomali_grafikleri.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Anomali grafikleri 'tez_anomali_grafikleri.png' olarak kaydedildi")

    def step5_clustering_analysis(self):
        """ADIM 5: Kümeleme Analizi"""
        print("\n" + "="*80)
        print("🎯 ADIM 5: KÜMELEME ANALİZİ")
        print("="*80)

        print("\n📋 5.1 NEDEN KÜMELEME ANALİZİ?")
        print("-" * 35)
        print("• Benzer özelliklere sahip gayrimenkulleri gruplamak")
        print("• Piyasa segmentlerini belirlemek")
        print("• Fiyatlandırma stratejileri geliştirmek")
        print("• Hedef müşteri gruplarını tanımlamak")

        # Veri hazırlama
        print(f"\n⚙️ 5.2 VERİ HAZIRLAMA")
        print("-" * 25)

        # Preprocessor oluştur
        numeric_transformer = Pipeline(steps=[
            ('imputer', SimpleImputer(strategy='median')),
            ('scaler', StandardScaler())
        ])

        categorical_transformer = Pipeline(steps=[
            ('imputer', SimpleImputer(strategy='most_frequent')),
            ('onehot', OneHotEncoder(handle_unknown='ignore', drop='first'))
        ])

        self.preprocessor = ColumnTransformer(
            transformers=[
                ('num', numeric_transformer, self.numeric_features),
                ('cat', categorical_transformer, self.categorical_features)
            ])

        X = self.df[self.numeric_features + self.categorical_features].copy()
        X_processed = self.preprocessor.fit_transform(X)

        # Sparse matrix'i dense'e çevir
        if hasattr(X_processed, 'toarray'):
            X_processed = X_processed.toarray()

        print(f"✅ Veri hazırlandı: {X_processed.shape[0]:,} kayıt × {X_processed.shape[1]} özellik")

        clustering_results = {}

        # K-Means Kümeleme
        print(f"\n🔵 5.3 K-MEANS KÜMELEME")
        print("-" * 30)
        print("K-Means Algoritması:")
        print("• Centroid tabanlı kümeleme")
        print("• Küme sayısı önceden belirlenir")
        print("• Euclidean mesafe kullanır")
        print("• Küresel kümeler oluşturur")

        # Optimal küme sayısını bul
        optimal_k = self._find_optimal_clusters(X_processed, max_k=8)
        print(f"• Optimal küme sayısı (Elbow): {optimal_k}")

        kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
        kmeans_labels = kmeans.fit_predict(X_processed)
        kmeans_silhouette = silhouette_score(X_processed, kmeans_labels)

        print(f"✅ Sonuçlar:")
        print(f"   • Küme sayısı: {optimal_k}")
        print(f"   • Silhouette skoru: {kmeans_silhouette:.3f}")
        print(f"   • Inertia: {kmeans.inertia_:.2f}")

        # Küme dağılımı
        unique, counts = np.unique(kmeans_labels, return_counts=True)
        print(f"   • Küme dağılımı:")
        for cluster, count in zip(unique, counts):
            percentage = (count / len(kmeans_labels)) * 100
            print(f"     - Küme {cluster}: {count:,} (%{percentage:.1f})")

        clustering_results['kmeans'] = {
            'n_clusters': optimal_k,
            'silhouette_score': kmeans_silhouette,
            'inertia': kmeans.inertia_,
            'labels': kmeans_labels,
            'cluster_distribution': dict(zip(unique, counts))
        }

        # Agglomerative Clustering
        print(f"\n🔗 5.4 AGGLOMERATİVE KÜMELEME")
        print("-" * 40)
        print("Agglomerative Clustering:")
        print("• Hiyerarşik kümeleme yöntemi")
        print("• Alt-yukarı (bottom-up) yaklaşım")
        print("• Ward linkage kullanır")
        print("• Dendogram oluşturabilir")

        agg_clustering = AgglomerativeClustering(n_clusters=optimal_k, linkage='ward')
        agg_labels = agg_clustering.fit_predict(X_processed)
        agg_silhouette = silhouette_score(X_processed, agg_labels)

        print(f"✅ Sonuçlar:")
        print(f"   • Küme sayısı: {optimal_k}")
        print(f"   • Silhouette skoru: {agg_silhouette:.3f}")
        print(f"   • Linkage: Ward")

        clustering_results['agglomerative'] = {
            'n_clusters': optimal_k,
            'silhouette_score': agg_silhouette,
            'labels': agg_labels
        }

        # DBSCAN
        print(f"\n🌐 5.5 DBSCAN KÜMELEME")
        print("-" * 25)
        print("DBSCAN Algoritması:")
        print("• Yoğunluk tabanlı kümeleme")
        print("• Küme sayısı otomatik belirlenir")
        print("• Gürültü noktalarını tespit eder")
        print("• Farklı şekillerde kümeler oluşturabilir")

        # DBSCAN parametrelerini optimize et
        eps_values = [0.3, 0.5, 0.7, 1.0]
        min_samples_values = [5, 10, 15]
        best_eps, best_min_samples, best_score = 0.5, 5, -1

        for eps in eps_values:
            for min_samples in min_samples_values:
                dbscan_temp = DBSCAN(eps=eps, min_samples=min_samples)
                labels_temp = dbscan_temp.fit_predict(X_processed)
                n_clusters_temp = len(set(labels_temp)) - (1 if -1 in labels_temp else 0)

                if n_clusters_temp > 1:
                    score_temp = silhouette_score(X_processed, labels_temp)
                    if score_temp > best_score:
                        best_score = score_temp
                        best_eps = eps
                        best_min_samples = min_samples

        dbscan = DBSCAN(eps=best_eps, min_samples=best_min_samples)
        dbscan_labels = dbscan.fit_predict(X_processed)
        n_clusters_dbscan = len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)
        n_noise = list(dbscan_labels).count(-1)

        print(f"✅ Sonuçlar:")
        print(f"   • Optimal eps: {best_eps}")
        print(f"   • Optimal min_samples: {best_min_samples}")
        print(f"   • Küme sayısı: {n_clusters_dbscan}")
        print(f"   • Gürültü noktası: {n_noise:,} (%{n_noise/len(dbscan_labels)*100:.1f})")

        if n_clusters_dbscan > 1:
            dbscan_silhouette = silhouette_score(X_processed, dbscan_labels)
            print(f"   • Silhouette skoru: {dbscan_silhouette:.3f}")
        else:
            dbscan_silhouette = -1
            print("   • Silhouette skoru: Hesaplanamadı (tek küme)")

        clustering_results['dbscan'] = {
            'n_clusters': n_clusters_dbscan,
            'n_noise': n_noise,
            'eps': best_eps,
            'min_samples': best_min_samples,
            'silhouette_score': dbscan_silhouette,
            'labels': dbscan_labels
        }

        # Kümeleme sonuçlarını DataFrame'e ekle
        self.df['Cluster_KMeans'] = kmeans_labels
        self.df['Cluster_Agglomerative'] = agg_labels
        self.df['Cluster_DBSCAN'] = dbscan_labels

        # Görselleştirme
        self._create_clustering_visualizations(clustering_results)

        # Sonuçları kaydet
        self.add_analysis_step(
            step_name="Kümeleme Analizi",
            description="K-Means, Agglomerative ve DBSCAN algoritmaları ile gayrimenkul segmentasyonu",
            method_reason="Benzer özelliklere sahip gayrimenkulleri gruplamak ve piyasa segmentlerini belirlemek için farklı kümeleme algoritmaları kullanıldı.",
            results=clustering_results,
            conclusion=f"Üç farklı kümeleme algoritması uygulandı. En iyi performans K-Means ile {optimal_k} küme oluşturularak elde edildi (Silhouette: {kmeans_silhouette:.3f})."
        )

        return True

    def _find_optimal_clusters(self, X, max_k=10):
        """Elbow yöntemi ile optimal küme sayısını bul"""
        inertias = []
        K_range = range(2, max_k + 1)

        for k in K_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(X)
            inertias.append(kmeans.inertia_)

        # Elbow noktasını bul (basit yaklaşım)
        diffs = np.diff(inertias)
        diffs2 = np.diff(diffs)
        optimal_k = K_range[np.argmax(diffs2) + 1] if len(diffs2) > 0 else 3

        return optimal_k

    def _create_clustering_visualizations(self, clustering_results):
        """Kümeleme görselleştirmeleri"""
        print("\n📈 Kümeleme grafikleri oluşturuluyor...")

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Kümeleme Analizi Sonuçları', fontsize=16, fontweight='bold')

        # 1. K-Means sonuçları
        kmeans_labels = clustering_results['kmeans']['labels']
        scatter1 = axes[0,0].scatter(self.df['Net_Metrekare'], self.df['Fiyat'],
                                   c=kmeans_labels, cmap='viridis', alpha=0.6, s=10)
        axes[0,0].set_title(f'K-Means Kümeleme (k={clustering_results["kmeans"]["n_clusters"]})')
        axes[0,0].set_xlabel('Net Metrekare (m²)')
        axes[0,0].set_ylabel('Fiyat (TL)')
        axes[0,0].ticklabel_format(style='plain', axis='y')
        plt.colorbar(scatter1, ax=axes[0,0])

        # 2. Agglomerative sonuçları
        agg_labels = clustering_results['agglomerative']['labels']
        scatter2 = axes[0,1].scatter(self.df['Net_Metrekare'], self.df['Fiyat'],
                                   c=agg_labels, cmap='plasma', alpha=0.6, s=10)
        axes[0,1].set_title(f'Agglomerative Kümeleme (k={clustering_results["agglomerative"]["n_clusters"]})')
        axes[0,1].set_xlabel('Net Metrekare (m²)')
        axes[0,1].set_ylabel('Fiyat (TL)')
        axes[0,1].ticklabel_format(style='plain', axis='y')
        plt.colorbar(scatter2, ax=axes[0,1])

        # 3. DBSCAN sonuçları
        dbscan_labels = clustering_results['dbscan']['labels']
        scatter3 = axes[1,0].scatter(self.df['Net_Metrekare'], self.df['Fiyat'],
                                   c=dbscan_labels, cmap='tab10', alpha=0.6, s=10)
        axes[1,0].set_title(f'DBSCAN Kümeleme (k={clustering_results["dbscan"]["n_clusters"]})')
        axes[1,0].set_xlabel('Net Metrekare (m²)')
        axes[1,0].set_ylabel('Fiyat (TL)')
        axes[1,0].ticklabel_format(style='plain', axis='y')
        plt.colorbar(scatter3, ax=axes[1,0])

        # 4. Silhouette skorları karşılaştırması
        methods = ['K-Means', 'Agglomerative', 'DBSCAN']
        scores = [
            clustering_results['kmeans']['silhouette_score'],
            clustering_results['agglomerative']['silhouette_score'],
            clustering_results['dbscan']['silhouette_score'] if clustering_results['dbscan']['silhouette_score'] > 0 else 0
        ]

        bars = axes[1,1].bar(methods, scores, color=['blue', 'green', 'red'], alpha=0.7)
        axes[1,1].set_title('Kümeleme Algoritmaları Silhouette Skorları')
        axes[1,1].set_ylabel('Silhouette Skoru')
        axes[1,1].set_ylim(0, max(scores) * 1.1)

        # Değerleri çubukların üzerine yaz
        for bar, score in zip(bars, scores):
            height = bar.get_height()
            axes[1,1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                          f'{score:.3f}', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('tez_kumeleme_grafikleri.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Kümeleme grafikleri 'tez_kumeleme_grafikleri.png' olarak kaydedildi")

    def generate_thesis_report(self):
        """Tez için kapsamlı rapor oluştur"""
        print("\n" + "="*80)
        print("📋 TEZ RAPORU OLUŞTURMA")
        print("="*80)

        report = []
        report.append("GAYRİMENKUL KARAR DESTEK SİSTEMİ - TEZ KAPSAMLI ANALİZ RAPORU")
        report.append("=" * 70)
        report.append(f"Analiz Tarihi: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Toplam Kayıt Sayısı: {len(self.df):,}")
        report.append("")

        # Her analiz adımını detaylı olarak yaz
        for i, step in enumerate(self.analysis_steps, 1):
            report.append(f"{i}. {step['step'].upper()}")
            report.append("-" * (len(step['step']) + 5))
            report.append("")

            report.append("AÇIKLAMA:")
            report.append(step['description'])
            report.append("")

            report.append("YÖNTEM SEÇİM NEDENİ:")
            report.append(step['method_reason'])
            report.append("")

            report.append("SONUÇLAR:")
            if isinstance(step['results'], dict):
                for key, value in step['results'].items():
                    if isinstance(value, (int, float)):
                        if isinstance(value, float):
                            report.append(f"• {key}: {value:.3f}")
                        else:
                            report.append(f"• {key}: {value:,}")
                    else:
                        report.append(f"• {key}: {value}")
            else:
                report.append(str(step['results']))
            report.append("")

            report.append("SONUÇ VE DEĞERLENDİRME:")
            report.append(step['conclusion'])
            report.append("")
            report.append("=" * 70)
            report.append("")

        # Genel değerlendirme
        report.append("GENEL DEĞERLENDİRME VE KARŞILAŞTIRMA")
        report.append("=" * 40)
        report.append("")

        report.append("1. VERİ KALİTESİ:")
        if len(self.analysis_steps) > 1:
            preprocessing_step = self.analysis_steps[1]  # Veri ön işleme adımı
            if 'data_quality_score' in preprocessing_step['results']:
                quality_score = preprocessing_step['results']['data_quality_score']
                report.append(f"• Veri kalite skoru: %{quality_score:.1f}")
                if quality_score >= 90:
                    report.append("• Değerlendirme: Çok iyi veri kalitesi")
                elif quality_score >= 80:
                    report.append("• Değerlendirme: İyi veri kalitesi")
                else:
                    report.append("• Değerlendirme: Orta veri kalitesi, iyileştirme gerekli")
        report.append("")

        report.append("2. ÖZELLİK MÜHENDİSLİĞİ ETKİSİ:")
        if len(self.analysis_steps) > 2:
            feature_step = self.analysis_steps[2]  # Özellik mühendisliği adımı
            if 'new_features_created' in feature_step['results']:
                new_features = feature_step['results']['new_features_created']
                total_features = feature_step['results']['total_features']
                report.append(f"• Oluşturulan yeni özellik sayısı: {new_features}")
                report.append(f"• Toplam özellik sayısı: {total_features}")
                report.append(f"• Özellik artış oranı: %{(new_features/total_features)*100:.1f}")
        report.append("")

        report.append("3. ANOMALİ TESPİTİ KARŞILAŞTIRMASI:")
        if len(self.analysis_steps) > 3:
            anomaly_step = self.analysis_steps[3]  # Anomali tespiti adımı
            if 'isolation_forest_percentage' in anomaly_step['results']:
                iso_pct = anomaly_step['results']['isolation_forest_percentage']
                svm_pct = anomaly_step['results']['one_class_svm_percentage']
                report.append(f"• Isolation Forest: %{iso_pct:.1f} anomali")
                report.append(f"• One-Class SVM: %{svm_pct:.1f} anomali")
                report.append(f"• Yöntemler arası fark: %{abs(iso_pct - svm_pct):.1f}")
                if abs(iso_pct - svm_pct) < 2:
                    report.append("• Değerlendirme: Yöntemler tutarlı sonuç veriyor")
                else:
                    report.append("• Değerlendirme: Yöntemler farklı sonuçlar veriyor")
        report.append("")

        report.append("4. KÜMELEME ALGORİTMALARI KARŞILAŞTIRMASI:")
        if len(self.analysis_steps) > 4:
            clustering_step = self.analysis_steps[4]  # Kümeleme adımı
            if 'kmeans' in clustering_step['results']:
                kmeans_score = clustering_step['results']['kmeans']['silhouette_score']
                agg_score = clustering_step['results']['agglomerative']['silhouette_score']
                dbscan_score = clustering_step['results']['dbscan']['silhouette_score']

                report.append(f"• K-Means Silhouette: {kmeans_score:.3f}")
                report.append(f"• Agglomerative Silhouette: {agg_score:.3f}")
                if dbscan_score > 0:
                    report.append(f"• DBSCAN Silhouette: {dbscan_score:.3f}")
                else:
                    report.append("• DBSCAN: Silhouette hesaplanamadı")

                best_method = "K-Means" if kmeans_score >= agg_score else "Agglomerative"
                if dbscan_score > max(kmeans_score, agg_score):
                    best_method = "DBSCAN"

                report.append(f"• En iyi kümeleme yöntemi: {best_method}")
        report.append("")

        # Öneriler
        report.append("5. ÖNERİLER:")
        report.append("• Veri kalitesini artırmak için düzenli veri temizleme yapılmalı")
        report.append("• Özellik mühendisliği ile model performansı artırılabilir")
        report.append("• Anomali tespiti ile piyasa dışı fiyatlar belirlenebilir")
        report.append("• Kümeleme analizi ile piyasa segmentasyonu yapılabilir")
        report.append("• Farklı algoritmaların ensemble kullanımı değerlendirilebilir")
        report.append("")

        # Raporu dosyaya kaydet
        report_text = "\n".join(report)
        with open('tez_kapsamli_rapor.txt', 'w', encoding='utf-8') as f:
            f.write(report_text)

        print("✅ Tez raporu 'tez_kapsamli_rapor.txt' dosyasına kaydedildi")
        print(f"📄 Rapor uzunluğu: {len(report)} satır")

        return report_text

    def run_complete_thesis_analysis(self):
        """Tez için tüm analizi çalıştır"""
        print("🎓 TEZ İÇİN KAPSAMLI GAYRİMENKUL ANALİZİ BAŞLIYOR...")
        print("=" * 70)

        start_time = pd.Timestamp.now()

        try:
            # Adım 1: Veri yükleme ve EDA
            if not self.step1_data_loading_and_exploration():
                return False

            # Adım 2: Veri ön işleme
            if not self.step2_data_preprocessing():
                return False

            # Adım 3: Özellik mühendisliği
            if not self.step3_feature_engineering():
                return False

            # Adım 4: Anomali tespiti
            if not self.step4_anomaly_detection():
                return False

            # Adım 5: Kümeleme analizi
            if not self.step5_clustering_analysis():
                return False

            # Rapor oluşturma
            self.generate_thesis_report()

            end_time = pd.Timestamp.now()
            duration = end_time - start_time

            print("\n" + "=" * 70)
            print("🎉 TEZ ANALİZİ TAMAMLANDI!")
            print("=" * 70)
            print(f"⏱️ Toplam süre: {duration}")
            print(f"📊 Analiz edilen kayıt sayısı: {len(self.df):,}")
            print(f"🔧 Kullanılan özellik sayısı: {len(self.numeric_features + self.categorical_features)}")
            print(f"📋 Tamamlanan analiz adımı: {len(self.analysis_steps)}")

            print("\n📁 Oluşturulan dosyalar:")
            print("   • tez_eda_grafikleri.png - Keşifsel veri analizi grafikleri")
            print("   • tez_anomali_grafikleri.png - Anomali tespiti grafikleri")
            print("   • tez_kumeleme_grafikleri.png - Kümeleme analizi grafikleri")
            print("   • tez_kapsamli_rapor.txt - Detaylı analiz raporu")

            print("\n📚 TEZ İÇİN KULLANILACAK BÖLÜMLER:")
            print("   1. Veri Yükleme ve EDA → Veri Seti Tanıtımı")
            print("   2. Veri Ön İşleme → Veri Hazırlama Yöntemleri")
            print("   3. Özellik Mühendisliği → Özellik Çıkarımı")
            print("   4. Anomali Tespiti → Aykırı Değer Analizi")
            print("   5. Kümeleme Analizi → Segmentasyon Yöntemleri")

            return True

        except Exception as e:
            print(f"\n❌ Analiz sırasında hata oluştu: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Ana fonksiyon"""
    import argparse

    parser = argparse.ArgumentParser(description='Tez için Kapsamlı Gayrimenkul Veri Madenciliği Analizi')
    parser.add_argument('--data', default='data/home_price.csv',
                       help='Veri dosyası yolu (varsayılan: data/home_price.csv)')

    args = parser.parse_args()

    # Analiz nesnesini oluştur ve çalıştır
    analyzer = TezKapsamliAnaliz(data_path=args.data)
    success = analyzer.run_complete_thesis_analysis()

    if success:
        print("\n✅ Tez analizi başarıyla tamamlandı!")
        print("📚 Tez yazımı için tüm materyaller hazır.")
    else:
        print("\n❌ Analiz tamamlanamadı!")
        return 1

    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())