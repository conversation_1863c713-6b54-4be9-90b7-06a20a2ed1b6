#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GAYRİMENKUL KARAR DESTEK SİSTEMİ - KAPSAMLI VERİ MADENCİLİĞİ VE MAKİNE ÖĞRENMESİ ANALİZİ
======================================================================================

Bu dosya uygulamanızdaki tüm veri madenciliği ve makine öğrenmesi yöntemlerini içerir:
- Veri ön işleme ve temizleme
- Özellik mühendisliği
- Aykırı değer tespiti (Isolation Forest)
- Sınıflandırma modelleri (SVM, Random Forest, XGBoost, Stacking)
- Regresyon modelleri (XGBoost, Random Forest)
- Kümeleme algoritmaları (K-Means, Agglomerative, DBSCAN, Gaussian Mixture)
- <PERSON><PERSON> indirgeme (PCA, Feature Selection)
- <PERSON><PERSON><PERSON> tespiti (Isolation Forest, One-Class SVM)
- Çok faktörlü yatırım uygunluk analizi
- Görselleştirme ve raporlama

Veri: home_price.csv (20,000+ gayrimenkul verisi)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Makine Öğrenmesi Kütüphaneleri
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline

# Sınıflandırma Modelleri
from sklearn.svm import LinearSVC
from sklearn.ensemble import RandomForestClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression

# Regresyon Modelleri
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression

# Kümeleme Algoritmaları
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN
from sklearn.mixture import GaussianMixture

# Anomali Tespiti
from sklearn.ensemble import IsolationForest
from sklearn.svm import OneClassSVM

# Boyut İndirgeme
from sklearn.decomposition import PCA
from sklearn.feature_selection import SelectKBest, f_regression

# Metrikler
from sklearn.metrics import (
    accuracy_score, classification_report, confusion_matrix,
    mean_squared_error, r2_score, mean_absolute_error,
    silhouette_score, adjusted_rand_score
)

# XGBoost (opsiyonel)
try:
    from xgboost import XGBClassifier, XGBRegressor
    XGBOOST_AVAILABLE = True
    print("✅ XGBoost mevcut")
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost mevcut değil, alternatif modeller kullanılacak")

# Grafik ayarları
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

class GayrimenkulKapsamliAnaliz:
    """Gayrimenkul verisi için kapsamlı veri madenciliği ve makine öğrenmesi analizi"""

    def __init__(self, data_path="home_price.csv"):
        self.data_path = data_path
        self.df = None
        self.df_original = None
        self.numeric_features = []
        self.categorical_features = []
        self.target_column = 'Fiyat'
        self.models = {}
        self.results = {}
        self.preprocessor = None

        print("🚀 GAYRİMENKUL KAPSAMLI ANALİZ SİSTEMİ")
        print("=" * 60)

    def load_data(self):
        """Veri yükleme"""
        print("\n📂 1. VERİ YÜKLEME")
        print("-" * 30)

        try:
            self.df = pd.read_csv(self.data_path)
            self.df_original = self.df.copy()
            print(f"✅ Veri başarıyla yüklendi: {len(self.df):,} satır, {len(self.df.columns)} sütun")

            print(f"\n📊 Sütunlar:")
            for i, col in enumerate(self.df.columns, 1):
                print(f"   {i:2d}. {col}")

            print(f"\n📈 Temel İstatistikler:")
            print(f"   • Fiyat aralığı: {self.df['Fiyat'].min():,.0f} - {self.df['Fiyat'].max():,.0f} TL")
            print(f"   • Ortalama fiyat: {self.df['Fiyat'].mean():,.0f} TL")
            print(f"   • Alan aralığı: {self.df['Net_Metrekare'].min():.0f} - {self.df['Net_Metrekare'].max():.0f} m²")

            return True

        except FileNotFoundError:
            print(f"❌ Veri dosyası bulunamadı: {self.data_path}")
            return False
        except Exception as e:
            print(f"❌ Veri yükleme hatası: {e}")
            return False

    def data_cleaning(self):
        """Veri temizleme ve ön işleme"""
        print("\n🧹 2. VERİ TEMİZLEME VE ÖN İŞLEME")
        print("-" * 40)

        # Eksik değer analizi
        missing_data = self.df.isnull().sum()
        missing_data = missing_data[missing_data > 0].sort_values(ascending=False)

        if len(missing_data) > 0:
            print("⚠️ Eksik değerler:")
            for col, count in missing_data.items():
                percentage = (count / len(self.df)) * 100
                print(f"   • {col}: {count:,} ({percentage:.1f}%)")
        else:
            print("✅ Eksik değer yok")

        # Geçersiz değerleri temizle
        initial_count = len(self.df)

        # Fiyat = 0 olan kayıtları kaldır
        self.df = self.df[self.df['Fiyat'] > 0]

        # Net metrekare = 0 olan kayıtları kaldır
        self.df = self.df[self.df['Net_Metrekare'] > 0]

        # Aşırı yüksek fiyatları filtrele (99.9 percentile üstü)
        price_threshold = self.df['Fiyat'].quantile(0.999)
        self.df = self.df[self.df['Fiyat'] <= price_threshold]

        # Aşırı büyük alanları filtrele (99.9 percentile üstü)
        area_threshold = self.df['Net_Metrekare'].quantile(0.999)
        self.df = self.df[self.df['Net_Metrekare'] <= area_threshold]

        cleaned_count = len(self.df)
        removed_count = initial_count - cleaned_count

        print(f"🗑️ Temizlenen kayıt sayısı: {removed_count:,}")
        print(f"✅ Kalan kayıt sayısı: {cleaned_count:,}")

        # Aykırı değer tespiti
        self._detect_outliers()

        return True

    def _detect_outliers(self):
        """Aykırı değer tespiti (IQR yöntemi)"""
        print("\n🔍 Aykırı Değer Tespiti:")

        numeric_cols = ['Net_Metrekare', 'Brüt_Metrekare', 'Oda_Sayısı', 'Fiyat', 'Banyo_Sayısı']

        for col in numeric_cols:
            if col in self.df.columns:
                Q1 = self.df[col].quantile(0.25)
                Q3 = self.df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers = self.df[(self.df[col] < lower_bound) | (self.df[col] > upper_bound)]
                outlier_count = len(outliers)

                if outlier_count > 0:
                    percentage = (outlier_count / len(self.df)) * 100
                    print(f"   • {col}: {outlier_count:,} aykırı değer ({percentage:.1f}%)")

    def feature_engineering(self):
        """Özellik mühendisliği"""
        print("\n🔧 3. ÖZELLİK MÜHENDİSLİĞİ")
        print("-" * 30)

        # Sayısal ve kategorik özellikleri ayır
        numeric_cols = ['Net_Metrekare', 'Brüt_Metrekare', 'Oda_Sayısı', 'Binanın_Kat_Sayısı', 'Banyo_Sayısı']
        categorical_cols = ['Şehir', 'Eşya_Durumu', 'Binanın_Yaşı', 'Isıtma_Tipi', 'Bulunduğu_Kat',
                           'Kullanım_Durumu', 'Yatırıma_Uygunluk', 'Takas', 'Tapu_Durumu']

        # Mevcut sütunları kontrol et
        self.numeric_features = [col for col in numeric_cols if col in self.df.columns]
        self.categorical_features = [col for col in categorical_cols if col in self.df.columns]

        print(f"📊 Sayısal özellikler ({len(self.numeric_features)}): {self.numeric_features}")
        print(f"📋 Kategorik özellikler ({len(self.categorical_features)}): {self.categorical_features}")

        # Yeni özellikler oluştur
        print("\n🆕 Yeni özellikler oluşturuluyor...")

        # 1. Fiyat/m² oranı
        self.df['Fiyat_Per_M2'] = self.df['Fiyat'] / self.df['Net_Metrekare']
        self.numeric_features.append('Fiyat_Per_M2')

        # 2. Oda başına alan
        if 'Oda_Sayısı' in self.df.columns:
            self.df['Alan_Per_Oda'] = self.df['Net_Metrekare'] / (self.df['Oda_Sayısı'] + 1)
            self.numeric_features.append('Alan_Per_Oda')

        # 3. Brüt/Net alan oranı
        if 'Brüt_Metrekare' in self.df.columns:
            self.df['Brut_Net_Oran'] = self.df['Brüt_Metrekare'] / self.df['Net_Metrekare']
            self.numeric_features.append('Brut_Net_Oran')

        # 4. Banyo/Oda oranı
        if 'Banyo_Sayısı' in self.df.columns and 'Oda_Sayısı' in self.df.columns:
            self.df['Banyo_Oda_Oran'] = self.df['Banyo_Sayısı'] / (self.df['Oda_Sayısı'] + 1)
            self.numeric_features.append('Banyo_Oda_Oran')

        # 5. Şehir büyüklük kategorisi
        if 'Şehir' in self.df.columns:
            city_counts = self.df['Şehir'].value_counts()
            self.df['Sehir_Buyukluk'] = self.df['Şehir'].map(city_counts)
            self.numeric_features.append('Sehir_Buyukluk')

        # 6. Yeni bina kategorisi
        if 'Binanın_Yaşı' in self.df.columns:
            self.df['Yeni_Bina'] = (self.df['Binanın_Yaşı'].str.contains('0 \(Yeni\)', na=False)).astype(int)
            self.numeric_features.append('Yeni_Bina')

        # 7. Lüks kategori (alan ve fiyat bazlı)
        area_threshold = self.df['Net_Metrekare'].quantile(0.8)
        price_threshold = self.df['Fiyat'].quantile(0.8)
        self.df['Luks_Kategori'] = ((self.df['Net_Metrekare'] >= area_threshold) &
                                   (self.df['Fiyat'] >= price_threshold)).astype(int)
        self.numeric_features.append('Luks_Kategori')

        print(f"✅ {len(self.numeric_features) - len(numeric_cols)} yeni özellik oluşturuldu")
        print(f"📊 Toplam sayısal özellik sayısı: {len(self.numeric_features)}")

        return True

    def create_preprocessor(self):
        """Veri ön işleme pipeline'ı oluştur"""
        print("\n⚙️ 4. VERİ ÖN İŞLEME PİPELİNE'I")
        print("-" * 35)

        # Sayısal özellikler için pipeline
        numeric_transformer = Pipeline(steps=[
            ('imputer', SimpleImputer(strategy='median')),
            ('scaler', StandardScaler())
        ])

        # Kategorik özellikler için pipeline
        categorical_transformer = Pipeline(steps=[
            ('imputer', SimpleImputer(strategy='most_frequent')),
            ('onehot', OneHotEncoder(handle_unknown='ignore', drop='first'))
        ])

        # Birleştirilmiş preprocessor
        self.preprocessor = ColumnTransformer(
            transformers=[
                ('num', numeric_transformer, self.numeric_features),
                ('cat', categorical_transformer, self.categorical_features)
            ])

        print("✅ Veri ön işleme pipeline'ı oluşturuldu")
        print(f"   • Sayısal özellikler: {len(self.numeric_features)}")
        print(f"   • Kategorik özellikler: {len(self.categorical_features)}")

        return True

    def anomaly_detection(self):
        """Anomali tespiti"""
        print("\n🚨 5. ANOMALİ TESPİTİ")
        print("-" * 25)

        # Sadece sayısal özellikler için anomali tespiti
        X_numeric = self.df[self.numeric_features].fillna(self.df[self.numeric_features].median())

        # Isolation Forest
        print("🌲 Isolation Forest ile anomali tespiti...")
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        anomaly_labels_iso = iso_forest.fit_predict(X_numeric)
        anomaly_count_iso = np.sum(anomaly_labels_iso == -1)

        print(f"   • Tespit edilen anomali sayısı: {anomaly_count_iso:,} ({anomaly_count_iso/len(self.df)*100:.1f}%)")

        # One-Class SVM
        print("🔍 One-Class SVM ile anomali tespiti...")
        try:
            oc_svm = OneClassSVM(nu=0.1)
            anomaly_labels_svm = oc_svm.fit_predict(X_numeric)
            anomaly_count_svm = np.sum(anomaly_labels_svm == -1)

            print(f"   • Tespit edilen anomali sayısı: {anomaly_count_svm:,} ({anomaly_count_svm/len(self.df)*100:.1f}%)")
        except Exception as e:
            print(f"   ⚠️ One-Class SVM hatası: {e}")

        # Anomali skorlarını kaydet
        self.df['Anomali_Skoru_ISO'] = iso_forest.decision_function(X_numeric)
        self.df['Anomali_Label_ISO'] = anomaly_labels_iso

        self.results['anomaly_detection'] = {
            'isolation_forest_anomalies': anomaly_count_iso,
            'isolation_forest_percentage': anomaly_count_iso/len(self.df)*100
        }

        return True

    def clustering_analysis(self):
        """Kümeleme analizi"""
        print("\n🎯 6. KÜMELEME ANALİZİ")
        print("-" * 25)

        # Veriyi hazırla
        X = self.df[self.numeric_features + self.categorical_features].copy()
        X_processed = self.preprocessor.fit_transform(X)

        # Sparse matrix'i dense'e çevir
        if hasattr(X_processed, 'toarray'):
            X_processed = X_processed.toarray()

        clustering_results = {}

        # K-Means Kümeleme
        print("🔵 K-Means kümeleme...")
        optimal_k = self._find_optimal_clusters(X_processed, max_k=10)
        kmeans = KMeans(n_clusters=optimal_k, random_state=42)
        kmeans_labels = kmeans.fit_predict(X_processed)
        kmeans_silhouette = silhouette_score(X_processed, kmeans_labels)

        print(f"   • Optimal küme sayısı: {optimal_k}")
        print(f"   • Silhouette skoru: {kmeans_silhouette:.3f}")

        clustering_results['kmeans'] = {
            'n_clusters': optimal_k,
            'silhouette_score': kmeans_silhouette,
            'labels': kmeans_labels
        }

        # Agglomerative Clustering
        print("🔗 Agglomerative kümeleme...")
        agg_clustering = AgglomerativeClustering(n_clusters=optimal_k)
        agg_labels = agg_clustering.fit_predict(X_processed)
        agg_silhouette = silhouette_score(X_processed, agg_labels)

        print(f"   • Küme sayısı: {optimal_k}")
        print(f"   • Silhouette skoru: {agg_silhouette:.3f}")

        clustering_results['agglomerative'] = {
            'n_clusters': optimal_k,
            'silhouette_score': agg_silhouette,
            'labels': agg_labels
        }

        # DBSCAN
        print("🌐 DBSCAN kümeleme...")
        dbscan = DBSCAN(eps=0.5, min_samples=5)
        dbscan_labels = dbscan.fit_predict(X_processed)
        n_clusters_dbscan = len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)
        n_noise = list(dbscan_labels).count(-1)

        print(f"   • Küme sayısı: {n_clusters_dbscan}")
        print(f"   • Gürültü noktası: {n_noise}")

        if n_clusters_dbscan > 1:
            dbscan_silhouette = silhouette_score(X_processed, dbscan_labels)
            print(f"   • Silhouette skoru: {dbscan_silhouette:.3f}")
        else:
            dbscan_silhouette = -1
            print("   • Silhouette skoru hesaplanamadı (tek küme)")

        clustering_results['dbscan'] = {
            'n_clusters': n_clusters_dbscan,
            'n_noise': n_noise,
            'silhouette_score': dbscan_silhouette,
            'labels': dbscan_labels
        }

        # Gaussian Mixture Model
        print("📊 Gaussian Mixture kümeleme...")
        gmm = GaussianMixture(n_components=optimal_k, random_state=42)
        gmm_labels = gmm.fit_predict(X_processed)
        gmm_silhouette = silhouette_score(X_processed, gmm_labels)

        print(f"   • Küme sayısı: {optimal_k}")
        print(f"   • Silhouette skoru: {gmm_silhouette:.3f}")
        print(f"   • AIC: {gmm.aic(X_processed):.2f}")
        print(f"   • BIC: {gmm.bic(X_processed):.2f}")

        clustering_results['gaussian_mixture'] = {
            'n_clusters': optimal_k,
            'silhouette_score': gmm_silhouette,
            'aic': gmm.aic(X_processed),
            'bic': gmm.bic(X_processed),
            'labels': gmm_labels
        }

        # Kümeleme sonuçlarını DataFrame'e ekle
        self.df['Cluster_KMeans'] = kmeans_labels
        self.df['Cluster_Agglomerative'] = agg_labels
        self.df['Cluster_DBSCAN'] = dbscan_labels
        self.df['Cluster_GMM'] = gmm_labels

        self.results['clustering'] = clustering_results

        return True

    def _find_optimal_clusters(self, X, max_k=10):
        """Elbow yöntemi ile optimal küme sayısını bul"""
        inertias = []
        K_range = range(2, max_k + 1)

        for k in K_range:
            kmeans = KMeans(n_clusters=k, random_state=42)
            kmeans.fit(X)
            inertias.append(kmeans.inertia_)

        # Elbow noktasını bul (basit yaklaşım)
        diffs = np.diff(inertias)
        diffs2 = np.diff(diffs)
        optimal_k = K_range[np.argmax(diffs2) + 1] if len(diffs2) > 0 else 3

        return optimal_k

    def classification_models(self):
        """Sınıflandırma modelleri"""
        print("\n🎯 7. SINIFLANDIRMA MODELLERİ")
        print("-" * 35)

        # Hedef değişkeni oluştur (fiyat kategorisi)
        price_quantiles = self.df['Fiyat'].quantile([0.33, 0.67])
        self.df['Fiyat_Kategori'] = pd.cut(self.df['Fiyat'],
                                          bins=[0, price_quantiles[0.33], price_quantiles[0.67], float('inf')],
                                          labels=[0, 1, 2])  # Sayısal etiketler kullan

        # Veriyi hazırla
        X = self.df[self.numeric_features + self.categorical_features].copy()
        y = self.df['Fiyat_Kategori']

        # Kategori isimlerini sakla
        category_names = {0: 'Düşük', 1: 'Orta', 2: 'Yüksek'}

        # Train-test split
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

        # Veriyi dönüştür
        X_train_processed = self.preprocessor.fit_transform(X_train)
        X_test_processed = self.preprocessor.transform(X_test)

        # Sparse matrix'i dense'e çevir
        if hasattr(X_train_processed, 'toarray'):
            X_train_processed = X_train_processed.toarray()
        if hasattr(X_test_processed, 'toarray'):
            X_test_processed = X_test_processed.toarray()

        classification_results = {}

        # Random Forest
        print("🌲 Random Forest sınıflandırması...")
        rf_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        rf_classifier.fit(X_train_processed, y_train)
        rf_pred = rf_classifier.predict(X_test_processed)
        rf_accuracy = accuracy_score(y_test, rf_pred)

        print(f"   • Doğruluk: {rf_accuracy:.3f}")

        classification_results['random_forest'] = {
            'accuracy': rf_accuracy,
            'model': rf_classifier,
            'predictions': rf_pred
        }

        # SVM
        print("⚡ SVM sınıflandırması...")
        svm_classifier = LinearSVC(random_state=42, max_iter=2000)
        svm_classifier.fit(X_train_processed, y_train)
        svm_pred = svm_classifier.predict(X_test_processed)
        svm_accuracy = accuracy_score(y_test, svm_pred)

        print(f"   • Doğruluk: {svm_accuracy:.3f}")

        classification_results['svm'] = {
            'accuracy': svm_accuracy,
            'model': svm_classifier,
            'predictions': svm_pred
        }

        # XGBoost (eğer mevcut ise)
        if XGBOOST_AVAILABLE:
            print("🚀 XGBoost sınıflandırması...")
            xgb_classifier = XGBClassifier(random_state=42)
            xgb_classifier.fit(X_train_processed, y_train)
            xgb_pred = xgb_classifier.predict(X_test_processed)
            xgb_accuracy = accuracy_score(y_test, xgb_pred)

            print(f"   • Doğruluk: {xgb_accuracy:.3f}")

            classification_results['xgboost'] = {
                'accuracy': xgb_accuracy,
                'model': xgb_classifier,
                'predictions': xgb_pred
            }

        # Stacking Classifier
        print("📚 Stacking sınıflandırması...")
        base_models = [
            ('rf', RandomForestClassifier(n_estimators=50, random_state=42)),
            ('svm', LinearSVC(random_state=42, max_iter=1000))
        ]

        if XGBOOST_AVAILABLE:
            base_models.append(('xgb', XGBClassifier(random_state=42)))

        stacking_classifier = StackingClassifier(
            estimators=base_models,
            final_estimator=LogisticRegression(random_state=42),
            cv=3
        )

        stacking_classifier.fit(X_train_processed, y_train)
        stacking_pred = stacking_classifier.predict(X_test_processed)
        stacking_accuracy = accuracy_score(y_test, stacking_pred)

        print(f"   • Doğruluk: {stacking_accuracy:.3f}")

        classification_results['stacking'] = {
            'accuracy': stacking_accuracy,
            'model': stacking_classifier,
            'predictions': stacking_pred
        }

        # En iyi modeli bul
        best_model_name = max(classification_results.keys(),
                             key=lambda x: classification_results[x]['accuracy'])
        best_accuracy = classification_results[best_model_name]['accuracy']

        print(f"\n🏆 En iyi model: {best_model_name.upper()} (Doğruluk: {best_accuracy:.3f})")

        self.results['classification'] = classification_results
        self.models['best_classifier'] = classification_results[best_model_name]['model']

        return True

    def regression_models(self):
        """Regresyon modelleri"""
        print("\n📈 8. REGRESYON MODELLERİ")
        print("-" * 30)

        # Veriyi hazırla
        X = self.df[self.numeric_features + self.categorical_features].copy()
        y = self.df['Fiyat']

        # Train-test split
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Veriyi dönüştür
        X_train_processed = self.preprocessor.fit_transform(X_train)
        X_test_processed = self.preprocessor.transform(X_test)

        # Sparse matrix'i dense'e çevir
        if hasattr(X_train_processed, 'toarray'):
            X_train_processed = X_train_processed.toarray()
        if hasattr(X_test_processed, 'toarray'):
            X_test_processed = X_test_processed.toarray()

        regression_results = {}

        # Random Forest Regressor
        print("🌲 Random Forest regresyonu...")
        rf_regressor = RandomForestRegressor(n_estimators=100, random_state=42)
        rf_regressor.fit(X_train_processed, y_train)
        rf_pred = rf_regressor.predict(X_test_processed)

        rf_mse = mean_squared_error(y_test, rf_pred)
        rf_rmse = np.sqrt(rf_mse)
        rf_mae = mean_absolute_error(y_test, rf_pred)
        rf_r2 = r2_score(y_test, rf_pred)

        print(f"   • RMSE: {rf_rmse:,.0f}")
        print(f"   • MAE: {rf_mae:,.0f}")
        print(f"   • R²: {rf_r2:.3f}")

        regression_results['random_forest'] = {
            'rmse': rf_rmse,
            'mae': rf_mae,
            'r2': rf_r2,
            'model': rf_regressor,
            'predictions': rf_pred
        }

        # Linear Regression
        print("📊 Linear regresyon...")
        lr_regressor = LinearRegression()
        lr_regressor.fit(X_train_processed, y_train)
        lr_pred = lr_regressor.predict(X_test_processed)

        lr_mse = mean_squared_error(y_test, lr_pred)
        lr_rmse = np.sqrt(lr_mse)
        lr_mae = mean_absolute_error(y_test, lr_pred)
        lr_r2 = r2_score(y_test, lr_pred)

        print(f"   • RMSE: {lr_rmse:,.0f}")
        print(f"   • MAE: {lr_mae:,.0f}")
        print(f"   • R²: {lr_r2:.3f}")

        regression_results['linear_regression'] = {
            'rmse': lr_rmse,
            'mae': lr_mae,
            'r2': lr_r2,
            'model': lr_regressor,
            'predictions': lr_pred
        }

        # XGBoost Regressor (eğer mevcut ise)
        if XGBOOST_AVAILABLE:
            print("🚀 XGBoost regresyonu...")
            xgb_regressor = XGBRegressor(random_state=42)
            xgb_regressor.fit(X_train_processed, y_train)
            xgb_pred = xgb_regressor.predict(X_test_processed)

            xgb_mse = mean_squared_error(y_test, xgb_pred)
            xgb_rmse = np.sqrt(xgb_mse)
            xgb_mae = mean_absolute_error(y_test, xgb_pred)
            xgb_r2 = r2_score(y_test, xgb_pred)

            print(f"   • RMSE: {xgb_rmse:,.0f}")
            print(f"   • MAE: {xgb_mae:,.0f}")
            print(f"   • R²: {xgb_r2:.3f}")

            regression_results['xgboost'] = {
                'rmse': xgb_rmse,
                'mae': xgb_mae,
                'r2': xgb_r2,
                'model': xgb_regressor,
                'predictions': xgb_pred
            }

        # En iyi modeli bul (R² skoruna göre)
        best_model_name = max(regression_results.keys(),
                             key=lambda x: regression_results[x]['r2'])
        best_r2 = regression_results[best_model_name]['r2']

        print(f"\n🏆 En iyi model: {best_model_name.upper()} (R²: {best_r2:.3f})")

        self.results['regression'] = regression_results
        self.models['best_regressor'] = regression_results[best_model_name]['model']

        return True

    def investment_suitability_analysis(self):
        """Çok faktörlü yatırım uygunluk analizi"""
        print("\n💰 9. YATIRIM UYGUNLUK ANALİZİ")
        print("-" * 35)

        # Çok faktörlü skorlama sistemi
        investment_scores = []

        for idx, row in self.df.iterrows():
            score = 0
            factors = []

            # 1. Fiyat/m² faktörü (30% ağırlık)
            price_per_m2 = row['Fiyat_Per_M2']
            city_avg_price = self.df[self.df['Şehir'] == row['Şehir']]['Fiyat_Per_M2'].median()

            if price_per_m2 <= city_avg_price * 0.8:
                price_score = 9  # Çok uygun
            elif price_per_m2 <= city_avg_price * 0.9:
                price_score = 7  # Uygun
            elif price_per_m2 <= city_avg_price * 1.1:
                price_score = 5  # Makul
            elif price_per_m2 <= city_avg_price * 1.3:
                price_score = 3  # Pahalı
            else:
                price_score = 1  # Çok pahalı

            score += price_score * 0.3
            factors.append(f"Fiyat: {price_score}")

            # 2. Alan faktörü (20% ağırlık)
            area = row['Net_Metrekare']
            if area >= 150:
                area_score = 9
            elif area >= 120:
                area_score = 7
            elif area >= 90:
                area_score = 5
            elif area >= 60:
                area_score = 3
            else:
                area_score = 1

            score += area_score * 0.2
            factors.append(f"Alan: {area_score}")

            # 3. Oda sayısı faktörü (15% ağırlık)
            rooms = row.get('Oda_Sayısı', 3)
            if rooms >= 5:
                room_score = 9
            elif rooms >= 4:
                room_score = 7
            elif rooms >= 3:
                room_score = 5
            elif rooms >= 2:
                room_score = 3
            else:
                room_score = 1

            score += room_score * 0.15
            factors.append(f"Oda: {room_score}")

            # 4. Şehir faktörü (15% ağırlık)
            city = row['Şehir'].lower()
            if city in ['istanbul', 'ankara', 'izmir']:
                city_score = 9
            elif city in ['antalya', 'bursa']:
                city_score = 7
            else:
                city_score = 5

            score += city_score * 0.15
            factors.append(f"Şehir: {city_score}")

            # 5. Bina yaşı faktörü (10% ağırlık)
            building_age = row.get('Binanın_Yaşı', '')
            if '0 (Yeni)' in str(building_age):
                age_score = 9
            elif '5-10' in str(building_age):
                age_score = 7
            elif '10-15' in str(building_age):
                age_score = 5
            elif '15-20' in str(building_age):
                age_score = 3
            else:
                age_score = 1

            score += age_score * 0.1
            factors.append(f"Yaş: {age_score}")

            # 6. Isıtma tipi faktörü (10% ağırlık)
            heating = row.get('Isıtma_Tipi', '')
            if 'Kombi' in str(heating) or 'Merkezi' in str(heating):
                heating_score = 9
            elif 'Klimalı' in str(heating):
                heating_score = 7
            else:
                heating_score = 5

            score += heating_score * 0.1
            factors.append(f"Isıtma: {heating_score}")

            investment_scores.append({
                'index': idx,
                'total_score': score,
                'factors': factors
            })

        # Skorları DataFrame'e ekle
        scores_df = pd.DataFrame(investment_scores)
        self.df['Yatirim_Skoru'] = scores_df['total_score']

        # Kategorilere ayır (9 kategori)
        score_bins = [0, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        score_labels = ['Çok Pahalı', 'Pahalı', 'Kısmen Pahalı', 'Belirsiz',
                       'Makul Fiyat', 'Kısmen Uygun', 'Uygun', 'Çok Uygun', 'Mükemmel']

        self.df['Yatirim_Kategori'] = pd.cut(self.df['Yatirim_Skoru'],
                                           bins=score_bins,
                                           labels=score_labels,
                                           include_lowest=True)

        # İstatistikler
        category_counts = self.df['Yatirim_Kategori'].value_counts()

        print("📊 Yatırım uygunluk dağılımı:")
        for category, count in category_counts.items():
            percentage = (count / len(self.df)) * 100
            print(f"   • {category}: {count:,} ({percentage:.1f}%)")

        print(f"\n📈 Ortalama yatırım skoru: {self.df['Yatirim_Skoru'].mean():.2f}")
        print(f"📊 En yüksek skor: {self.df['Yatirim_Skoru'].max():.2f}")
        print(f"📉 En düşük skor: {self.df['Yatirim_Skoru'].min():.2f}")

        self.results['investment_analysis'] = {
            'category_distribution': category_counts.to_dict(),
            'average_score': self.df['Yatirim_Skoru'].mean(),
            'max_score': self.df['Yatirim_Skoru'].max(),
            'min_score': self.df['Yatirim_Skoru'].min()
        }

        return True

    def visualizations(self):
        """Görselleştirmeler"""
        print("\n📊 10. GÖRSELLEŞTİRMELER")
        print("-" * 30)

        # Grafik boyutunu ayarla
        plt.figure(figsize=(20, 15))

        # 1. Fiyat dağılımı
        plt.subplot(3, 3, 1)
        plt.hist(self.df['Fiyat'], bins=50, alpha=0.7, color='skyblue')
        plt.title('Fiyat Dağılımı')
        plt.xlabel('Fiyat (TL)')
        plt.ylabel('Frekans')
        plt.ticklabel_format(style='plain', axis='x')

        # 2. Alan dağılımı
        plt.subplot(3, 3, 2)
        plt.hist(self.df['Net_Metrekare'], bins=50, alpha=0.7, color='lightgreen')
        plt.title('Net Metrekare Dağılımı')
        plt.xlabel('Net Metrekare (m²)')
        plt.ylabel('Frekans')

        # 3. Fiyat-Alan ilişkisi
        plt.subplot(3, 3, 3)
        plt.scatter(self.df['Net_Metrekare'], self.df['Fiyat'], alpha=0.5, s=10)
        plt.title('Fiyat-Alan İlişkisi')
        plt.xlabel('Net Metrekare (m²)')
        plt.ylabel('Fiyat (TL)')
        plt.ticklabel_format(style='plain', axis='y')

        # 4. Şehirlere göre ortalama fiyat
        plt.subplot(3, 3, 4)
        city_prices = self.df.groupby('Şehir')['Fiyat'].mean().sort_values(ascending=False).head(10)
        city_prices.plot(kind='bar')
        plt.title('Şehirlere Göre Ortalama Fiyat (Top 10)')
        plt.xlabel('Şehir')
        plt.ylabel('Ortalama Fiyat (TL)')
        plt.xticks(rotation=45)
        plt.ticklabel_format(style='plain', axis='y')

        # 5. Oda sayısına göre fiyat dağılımı
        plt.subplot(3, 3, 5)
        if 'Oda_Sayısı' in self.df.columns:
            room_counts = self.df['Oda_Sayısı'].value_counts().sort_index()
            room_counts.plot(kind='bar')
            plt.title('Oda Sayısı Dağılımı')
            plt.xlabel('Oda Sayısı')
            plt.ylabel('Gayrimenkul Sayısı')

        # 6. Yatırım uygunluk dağılımı
        plt.subplot(3, 3, 6)
        if 'Yatirim_Kategori' in self.df.columns:
            investment_dist = self.df['Yatirim_Kategori'].value_counts()
            plt.pie(investment_dist.values, labels=investment_dist.index, autopct='%1.1f%%')
            plt.title('Yatırım Uygunluk Dağılımı')

        # 7. Fiyat/m² dağılımı
        plt.subplot(3, 3, 7)
        plt.hist(self.df['Fiyat_Per_M2'], bins=50, alpha=0.7, color='orange')
        plt.title('Fiyat/m² Dağılımı')
        plt.xlabel('Fiyat/m² (TL)')
        plt.ylabel('Frekans')

        # 8. Kümeleme sonuçları (K-Means)
        plt.subplot(3, 3, 8)
        if 'Cluster_KMeans' in self.df.columns:
            scatter = plt.scatter(self.df['Net_Metrekare'], self.df['Fiyat'],
                                c=self.df['Cluster_KMeans'], cmap='viridis', alpha=0.6, s=10)
            plt.title('K-Means Kümeleme Sonuçları')
            plt.xlabel('Net Metrekare (m²)')
            plt.ylabel('Fiyat (TL)')
            plt.colorbar(scatter)
            plt.ticklabel_format(style='plain', axis='y')

        # 9. Anomali tespiti sonuçları
        plt.subplot(3, 3, 9)
        if 'Anomali_Label_ISO' in self.df.columns:
            normal_data = self.df[self.df['Anomali_Label_ISO'] == 1]
            anomaly_data = self.df[self.df['Anomali_Label_ISO'] == -1]

            plt.scatter(normal_data['Net_Metrekare'], normal_data['Fiyat'],
                       c='blue', alpha=0.6, s=10, label='Normal')
            plt.scatter(anomaly_data['Net_Metrekare'], anomaly_data['Fiyat'],
                       c='red', alpha=0.8, s=20, label='Anomali')
            plt.title('Anomali Tespiti Sonuçları')
            plt.xlabel('Net Metrekare (m²)')
            plt.ylabel('Fiyat (TL)')
            plt.legend()
            plt.ticklabel_format(style='plain', axis='y')

        plt.tight_layout()
        plt.savefig('gayrimenkul_analiz_grafikleri.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Grafikler oluşturuldu ve 'gayrimenkul_analiz_grafikleri.png' olarak kaydedildi")

        return True

    def generate_report(self):
        """Kapsamlı analiz raporu oluştur"""
        print("\n📋 11. KAPSAMLI ANALİZ RAPORU")
        print("=" * 40)

        report = []
        report.append("GAYRİMENKUL KARAR DESTEK SİSTEMİ - KAPSAMLI ANALİZ RAPORU")
        report.append("=" * 60)
        report.append(f"Analiz Tarihi: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Toplam Kayıt Sayısı: {len(self.df):,}")
        report.append("")

        # Veri özeti
        report.append("1. VERİ ÖZETİ")
        report.append("-" * 15)
        report.append(f"• Fiyat aralığı: {self.df['Fiyat'].min():,.0f} - {self.df['Fiyat'].max():,.0f} TL")
        report.append(f"• Ortalama fiyat: {self.df['Fiyat'].mean():,.0f} TL")
        report.append(f"• Medyan fiyat: {self.df['Fiyat'].median():,.0f} TL")
        report.append(f"• Alan aralığı: {self.df['Net_Metrekare'].min():.0f} - {self.df['Net_Metrekare'].max():.0f} m²")
        report.append(f"• Ortalama alan: {self.df['Net_Metrekare'].mean():.0f} m²")
        report.append("")

        # Anomali tespiti sonuçları
        if 'anomaly_detection' in self.results:
            report.append("2. ANOMALİ TESPİTİ")
            report.append("-" * 18)
            anomaly_results = self.results['anomaly_detection']
            report.append(f"• Isolation Forest ile tespit edilen anomali: {anomaly_results['isolation_forest_anomalies']:,} ({anomaly_results['isolation_forest_percentage']:.1f}%)")
            report.append("")

        # Kümeleme sonuçları
        if 'clustering' in self.results:
            report.append("3. KÜMELEME ANALİZİ")
            report.append("-" * 20)
            clustering_results = self.results['clustering']
            for method, results in clustering_results.items():
                if method == 'dbscan':
                    report.append(f"• {method.upper()}: {results['n_clusters']} küme, {results['n_noise']} gürültü")
                else:
                    report.append(f"• {method.upper()}: {results['n_clusters']} küme, Silhouette: {results['silhouette_score']:.3f}")
            report.append("")

        # Sınıflandırma sonuçları
        if 'classification' in self.results:
            report.append("4. SINIFLANDIRMA MODELLERİ")
            report.append("-" * 28)
            classification_results = self.results['classification']
            for model, results in classification_results.items():
                report.append(f"• {model.upper()}: Doğruluk {results['accuracy']:.3f}")
            report.append("")

        # Regresyon sonuçları
        if 'regression' in self.results:
            report.append("5. REGRESYON MODELLERİ")
            report.append("-" * 23)
            regression_results = self.results['regression']
            for model, results in regression_results.items():
                report.append(f"• {model.upper()}: R² {results['r2']:.3f}, RMSE {results['rmse']:,.0f}")
            report.append("")

        # Yatırım analizi sonuçları
        if 'investment_analysis' in self.results:
            report.append("6. YATIRIM UYGUNLUK ANALİZİ")
            report.append("-" * 30)
            investment_results = self.results['investment_analysis']
            report.append(f"• Ortalama yatırım skoru: {investment_results['average_score']:.2f}")
            report.append(f"• En yüksek skor: {investment_results['max_score']:.2f}")
            report.append(f"• En düşük skor: {investment_results['min_score']:.2f}")
            report.append("")
            report.append("Kategori Dağılımı:")
            for category, count in investment_results['category_distribution'].items():
                percentage = (count / len(self.df)) * 100
                report.append(f"  - {category}: {count:,} ({percentage:.1f}%)")
            report.append("")

        # Öneriler
        report.append("7. ÖNERİLER VE SONUÇLAR")
        report.append("-" * 25)

        # En iyi performans gösteren modeli bul
        if 'classification' in self.results:
            best_classifier = max(self.results['classification'].items(),
                                key=lambda x: x[1]['accuracy'])
            report.append(f"• En iyi sınıflandırma modeli: {best_classifier[0].upper()} (Doğruluk: {best_classifier[1]['accuracy']:.3f})")

        if 'regression' in self.results:
            best_regressor = max(self.results['regression'].items(),
                               key=lambda x: x[1]['r2'])
            report.append(f"• En iyi regresyon modeli: {best_regressor[0].upper()} (R²: {best_regressor[1]['r2']:.3f})")

        # Yatırım önerileri
        if 'investment_analysis' in self.results:
            avg_score = investment_results['average_score']
            if avg_score >= 7:
                report.append("• Genel yatırım durumu: ÇOK UYGUN")
            elif avg_score >= 5:
                report.append("• Genel yatırım durumu: UYGUN")
            else:
                report.append("• Genel yatırım durumu: DİKKATLİ OLUNMALI")

        report.append("")
        report.append("8. TEKNİK DETAYLAR")
        report.append("-" * 20)
        report.append(f"• Kullanılan özellik sayısı: {len(self.numeric_features + self.categorical_features)}")
        report.append(f"• Sayısal özellikler: {len(self.numeric_features)}")
        report.append(f"• Kategorik özellikler: {len(self.categorical_features)}")
        report.append(f"• Oluşturulan yeni özellikler: {len([f for f in self.numeric_features if f not in ['Net_Metrekare', 'Brüt_Metrekare', 'Oda_Sayısı', 'Binanın_Kat_Sayısı', 'Banyo_Sayısı']])}")

        # Raporu dosyaya kaydet
        report_text = "\n".join(report)
        with open('gayrimenkul_analiz_raporu.txt', 'w', encoding='utf-8') as f:
            f.write(report_text)

        # Raporu ekrana yazdır
        print(report_text)
        print("\n✅ Rapor 'gayrimenkul_analiz_raporu.txt' dosyasına kaydedildi")

        return True

    def run_complete_analysis(self):
        """Tüm analizi çalıştır"""
        print("🚀 KAPSAMLI GAYRİMENKUL ANALİZİ BAŞLIYOR...")
        print("=" * 60)

        start_time = pd.Timestamp.now()

        try:
            # 1. Veri yükleme
            if not self.load_data():
                return False

            # 2. Veri temizleme
            if not self.data_cleaning():
                return False

            # 3. Özellik mühendisliği
            if not self.feature_engineering():
                return False

            # 4. Veri ön işleme pipeline'ı
            if not self.create_preprocessor():
                return False

            # 5. Anomali tespiti
            if not self.anomaly_detection():
                return False

            # 6. Kümeleme analizi
            if not self.clustering_analysis():
                return False

            # 7. Sınıflandırma modelleri
            if not self.classification_models():
                return False

            # 8. Regresyon modelleri
            if not self.regression_models():
                return False

            # 9. Yatırım uygunluk analizi
            if not self.investment_suitability_analysis():
                return False

            # 10. Görselleştirmeler
            if not self.visualizations():
                return False

            # 11. Rapor oluşturma
            if not self.generate_report():
                return False

            end_time = pd.Timestamp.now()
            duration = end_time - start_time

            print("\n" + "=" * 60)
            print("🎉 KAPSAMLI ANALİZ TAMAMLANDI!")
            print("=" * 60)
            print(f"⏱️ Toplam süre: {duration}")
            print(f"📊 Analiz edilen kayıt sayısı: {len(self.df):,}")
            print(f"🔧 Kullanılan özellik sayısı: {len(self.numeric_features + self.categorical_features)}")
            print(f"🤖 Eğitilen model sayısı: {len(self.models)}")

            print("\n📁 Oluşturulan dosyalar:")
            print("   • gayrimenkul_analiz_grafikleri.png")
            print("   • gayrimenkul_analiz_raporu.txt")

            return True

        except Exception as e:
            print(f"\n❌ Analiz sırasında hata oluştu: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Ana fonksiyon"""
    import argparse

    parser = argparse.ArgumentParser(description='Gayrimenkul Kapsamlı Veri Madenciliği ve Makine Öğrenmesi Analizi')
    parser.add_argument('--data', default='data/home_price.csv',
                       help='Veri dosyası yolu (varsayılan: data/home_price.csv)')

    args = parser.parse_args()

    # Analiz nesnesini oluştur ve çalıştır
    analyzer = GayrimenkulKapsamliAnaliz(data_path=args.data)
    success = analyzer.run_complete_analysis()

    if success:
        print("\n✅ Analiz başarıyla tamamlandı!")
        print("📊 Detaylı sonuçlar için 'gayrimenkul_analiz_raporu.txt' dosyasını inceleyebilirsiniz.")
        print("📈 Grafikler için 'gayrimenkul_analiz_grafikleri.png' dosyasını açabilirsiniz.")
    else:
        print("\n❌ Analiz tamamlanamadı!")
        return 1

    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())