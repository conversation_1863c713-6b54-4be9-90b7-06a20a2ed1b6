# 🚀 TAM KAPASİTE MODU - Gayrimenkul Karar Destek Sistemi

## 🎯 TAM KAPASİTE MODU NEDİR?

TAM KAPASİTE MODU, Gayrimenkul Karar Destek Sistemi'nin maksimum performans ve kapasitede çalışması için özel olarak optimize edilmiş bir konfigürasyondur.

## ✨ TAM KAPASİTE ÖZELLİKLERİ

### 🚀 **Performans Optimizasyonları**
- **Unlimited Data Processing**: Veri limiti kaldırıldı
- **Memory Optimization**: Optimal veri tipleri ve memory kullanımı
- **Vectorized Operations**: NumPy ve Pandas vectorized işlemler
- **Parallel Processing**: Çoklu işlemci desteği
- **Advanced Caching**: Akıllı önbellek sistemi
- **Chunk-based Processing**: Büyük veri setleri için parçalı işlem

### 📊 **Gelişmiş ML Özellikleri**
- **100K+ Örnek Veri**: Normal modda 1K, TAM KAPASİTE'de 100K
- **Advanced Models**: XGBoost, LightGBM, CatBoost desteği
- **Ensemble Methods**: Çoklu model kombinasyonu
- **Hyperparameter Optimization**: Optuna ile otomatik optimizasyon
- **Real-time Performance Monitoring**: Anlık performans izleme

### 🔧 **Backend Optimizasyonları**
- **100MB Request Size**: Normal modda 10MB, TAM KAPASİTE'de 100MB
- **2 Dakika Timeout**: Normal modda 30 saniye, TAM KAPASİTE'de 2 dakika
- **Maximum Compression**: Level 9 sıkıştırma
- **100K Parameter Limit**: Normal modda 1K, TAM KAPASİTE'de 100K
- **Advanced CORS**: Uzun süreli cache

## 🚀 NASIL BAŞLATILIR?

### 1. **Otomatik Başlatma (Önerilen)**
```bash
# Windows
start-full-capacity.bat

# Linux/Mac
chmod +x start-full-capacity.sh
./start-full-capacity.sh
```

### 2. **Manuel Başlatma**

#### ML API (Python)
```bash
cd ml-models
pip install -r requirements.txt
python api.py
```

#### Backend (Node.js)
```bash
cd backend
npm install
npm run full-capacity
```

#### Frontend (React)
```bash
cd frontend
npm install
npm start
```

## 📊 SİSTEM GEREKSİNİMLERİ

### **Minimum Gereksinimler**
- **RAM**: 8GB (16GB önerilen)
- **CPU**: 4 çekirdek (8 çekirdek önerilen)
- **Disk**: 10GB boş alan
- **Python**: 3.8+
- **Node.js**: 18.0+

### **Optimal Performans İçin**
- **RAM**: 32GB+
- **CPU**: 16+ çekirdek
- **SSD**: NVMe SSD
- **GPU**: CUDA destekli (opsiyonel)

## 🔍 PERFORMANS İZLEME

### **Endpoint'ler**
- **Backend Stats**: `http://localhost:3001/api/stats`
- **ML Performance**: `http://localhost:5000/api/performance`
- **System Health**: `http://localhost:3001/health`

### **Performans Metrikleri**
- Cache hit rate
- Ortalama tahmin süresi
- Memory kullanımı
- İşlenen veri miktarı
- Model doğruluğu

## ⚙️ KONFIGÜRASYON

### **Environment Variables**
```bash
FULL_CAPACITY_MODE=true
MAX_REQUEST_SIZE=100mb
REQUEST_TIMEOUT=120000
CACHE_ENABLED=true
PARALLEL_PROCESSING=true
MEMORY_OPTIMIZATION=true
```

### **Özelleştirme**
- `.env.full-capacity` dosyasını düzenleyin
- `data_processor.py` içinde batch_size ayarlayın
- `api.py` içinde cache_size değiştirin

## 🎯 PERFORMANS KARŞILAŞTIRMASI

| Özellik | Normal Mod | TAM KAPASİTE |
|---------|------------|--------------|
| Veri Limiti | 1,000 kayıt | Unlimited |
| Request Size | 10MB | 100MB |
| Timeout | 30 saniye | 2 dakika |
| Cache | Basit | Gelişmiş |
| Compression | Level 6 | Level 9 |
| Memory | Standard | Optimized |
| Parallel | Hayır | Evet |

## 🔧 SORUN GİDERME

### **Yaygın Sorunlar**
1. **Memory Hatası**: RAM'i artırın veya batch_size'ı küçültün
2. **Timeout**: REQUEST_TIMEOUT değerini artırın
3. **Port Çakışması**: Port numaralarını değiştirin
4. **Dependency Hatası**: `pip install -r requirements.txt` çalıştırın

### **Log Dosyaları**
- Backend: Console output
- ML API: Python console
- Frontend: Browser console

## 📈 PERFORMANS İPUÇLARI

1. **SSD Kullanın**: Veri okuma hızı için kritik
2. **RAM Artırın**: Büyük veri setleri için gerekli
3. **CPU Çekirdek Sayısı**: Paralel işlem için önemli
4. **Antivirus**: Geçici olarak devre dışı bırakın
5. **Background Apps**: Gereksiz uygulamaları kapatın

## 🎉 BAŞARI GÖSTERGELERİ

TAM KAPASİTE modu başarıyla çalışıyorsa:
- ✅ 100K+ veri kaydı işleniyor
- ✅ Cache hit rate %80+
- ✅ Ortalama tahmin süresi <100ms
- ✅ Memory kullanımı optimize
- ✅ Tüm endpoint'ler yanıt veriyor

---

**🚀 TAM KAPASİTE MODU AKTİF - SİSTEM HAZIR!**
