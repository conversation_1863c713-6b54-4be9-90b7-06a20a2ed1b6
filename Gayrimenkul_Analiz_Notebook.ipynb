{
  "cells": [
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "introduction"
      },
      "source": [
        "# Gayrimenkul Veri Analizi ve Makine Öğrenmesi\n",
        "\n",
        "Bu notebook, gayrimenkul verileri üzerinde çeşitli veri madenciliği ve makine öğrenmesi yöntemlerini uygulamaktadır.\n",
        "\n",
        "## İçerik\n",
        "1. Veri Yükleme ve İnceleme\n",
        "2. <PERSON><PERSON> Ön İşleme\n",
        "3. <PERSON><PERSON><PERSON><PERSON>eri Analizi (EDA)\n",
        "4. <PERSON><PERSON><PERSON> Mühendisliği\n",
        "5. Sınıflandırma Modelleri\n",
        "6. Regresyon Modelleri\n",
        "7. Kümeleme Analizi\n",
        "8. <PERSON><PERSON><PERSON>\n",
        "9. <PERSON> Değerlendirme\n",
        "10. Yatır<PERSON><PERSON>"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "setup"
      },
      "source": [
        "## 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON>"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "install_libraries"
      },
      "outputs": [],
      "source": [
        "# Gerekli kütüphanelerin yüklenmesi\n",
        "!pip install xgboost scikit-learn pandas numpy matplotlib seaborn joblib"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "import_libraries"
      },
      "outputs": [],
      "source": [
        "# Gerekli kütüphanelerin import edilmesi\n",
        "import numpy as np\n",
        "import pandas as pd\n",
        "import matplotlib.pyplot as plt\n",
        "import seaborn as sns\n",
        "from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score\n",
        "from sklearn.preprocessing import StandardScaler, OneHotEncoder, LabelEncoder\n",
        "from sklearn.compose import ColumnTransformer\n",
        "from sklearn.pipeline import Pipeline\n",
        "from sklearn.impute import SimpleImputer\n",
        "from sklearn.svm import LinearSVC, SVR\n",
        "from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor, StackingClassifier, IsolationForest\n",
        "from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN\n",
        "from sklearn.mixture import GaussianMixture\n",
        "from sklearn.linear_model import LogisticRegression, LinearRegression\n",
        "from sklearn.metrics import (\n",
        "    classification_report, accuracy_score, silhouette_score,\n",
        "    mean_absolute_error, mean_squared_error, r2_score\n",
        ")\n",
        "from xgboost import XGBClassifier, XGBRegressor\n",
        "import joblib\n",
        "import warnings\n",
        "warnings.filterwarnings('ignore')\n",
        "\n",
        "# Görselleştirme ayarları\n",
        "plt.style.use('seaborn-v0_8-whitegrid')\n",
        "sns.set_palette('viridis')\n",
        "plt.rcParams['figure.figsize'] = (12, 8)\n",
        "plt.rcParams['font.size'] = 12"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "data_loading"
      },
      "source": [
        "## 2. Veri Yükleme ve İnceleme\n",
        "\n",
        "Öncelikle, veri setimizi yükleyelim. Eğer veri seti mevcut değilse, örnek bir veri seti oluşturacağız."
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "upload_data"
      },
      "outputs": [],
      "source": [
        "# Google Colab'da dosya yükleme\n",
        "from google.colab import files\n",
        "uploaded = files.upload()  # home_price.csv dosyasını yükleyin"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "load_data"
      },
      "outputs": [],
      "source": [
        "# Veri setini yükleme\n",
        "try:\n",
        "    df = pd.read_csv('home_price.csv')\n",
        "    print(f\"Veri seti başarıyla yüklendi. Boyut: {df.shape}\")\n",
        "except FileNotFoundError:\n",
        "    print(\"Veri seti bulunamadı. Örnek veri seti oluşturuluyor...\")\n",
        "    # Örnek veri seti oluşturma\n",
        "    n_samples = 1000\n",
        "    \n",
        "    # Şehir kodları ve fiyat aralıkları\n",
        "    city_codes = {\n",
        "        'İstanbul': 0,\n",
        "        'Ankara': 1,\n",
        "        'İzmir': 2,\n",
        "        'Bursa': 3,\n",
        "        'Antalya': 4,\n",
        "        'Adana': 5,\n",
        "        'Konya': 6,\n",
        "        'Kayseri': 7,\n",
        "        'Trabzon': 8,\n",
        "        'Samsun': 9\n",
        "    }\n",
        "    \n",
        "    city_price_ranges = {\n",
        "        0: (800000, 5000000),  # İstanbul\n",
        "        1: (500000, 3000000),  # Ankara\n",
        "        2: (600000, 3500000),  # İzmir\n",
        "        3: (450000, 2500000),  # Bursa\n",
        "        4: (500000, 3000000),  # Antalya\n",
        "        5: (350000, 1800000),  # Adana\n",
        "        6: (300000, 1500000),  # Konya\n",
        "        7: (300000, 1500000),  # Kayseri\n",
        "        8: (350000, 1800000),  # Trabzon\n",
        "        9: (300000, 1500000)   # Samsun\n",
        "    }\n",
        "    \n",
        "    # Rastgele veri oluşturma\n",
        "    np.random.seed(42)\n",
        "    \n",
        "    # Şehir\n",
        "    city = np.random.choice(list(range(10)), n_samples, p=[0.3, 0.2, 0.15, 0.1, 0.1, 0.05, 0.03, 0.03, 0.02, 0.02])\n",
        "    \n",
        "    # Metrekare\n",
        "    net_area = np.random.randint(50, 250, n_samples)\n",
        "    gross_area = net_area * np.random.uniform(1.1, 1.3, n_samples)\n",
        "    gross_area = gross_area.astype(int)\n",
        "    \n",
        "    # Oda sayısı\n",
        "    room_count = np.random.choice([1, 2, 3, 4, 5, 6], n_samples, p=[0.05, 0.15, 0.4, 0.3, 0.07, 0.03])\n",
        "    \n",
        "    # Banyo sayısı\n",
        "    banyo_sayisi = np.random.choice([1, 2, 3, 4], n_samples, p=[0.4, 0.4, 0.15, 0.05])\n",
        "    \n",
        "    # Bina yaşı\n",
        "    building_age_codes = {\n",
        "        '0-5': 0,\n",
        "        '5-10': 1,\n",
        "        '10-15': 2,\n",
        "        '15-20': 3,\n",
        "        '20-25': 4,\n",
        "        '25+': 5\n",
        "    }\n",
        "    building_age = np.random.choice(list(building_age_codes.values()), n_samples, p=[0.2, 0.25, 0.2, 0.15, 0.1, 0.1])\n",
        "    \n",
        "    # Isıtma tipi\n",
        "    heating_type_codes = {\n",
        "        'Yerden Isıtma': 0,\n",
        "        'Kombi Doğalgaz': 1,\n",
        "        'Merkezi Doğalgaz': 2,\n",
        "        'Klima': 3,\n",
        "        'Soba': 4,\n",
        "        'Diğer': 5\n",
        "    }\n",
        "    heating_type = np.random.choice(list(heating_type_codes.values()), n_samples, p=[0.1, 0.4, 0.3, 0.1, 0.05, 0.05])\n",
        "    \n",
        "    # Eşya durumu\n",
        "    esya_durumu_codes = {\n",
        "        'Eşyalı': 0,\n",
        "        'Yarı Eşyalı': 1,\n",
        "        'Boş': 2\n",
        "    }\n",
        "    esya_durumu = np.random.choice(list(esya_durumu_codes.values()), n_samples, p=[0.2, 0.3, 0.5])\n",
        "    \n",
        "    # Fiyat hesaplama\n",
        "    price = []\n",
        "    for i in range(n_samples):\n",
        "        # Temel fiyat (şehire göre)\n",
        "        base_price = np.random.randint(*city_price_ranges[city[i]])\n",
        "        \n",
        "        # Metrekare etkisi\n",
        "        area_factor = net_area[i] * 5000\n",
        "        \n",
        "        # Oda sayısı etkisi\n",
        "        room_factor = room_count[i] * 100000\n",
        "        \n",
        "        # Banyo sayısı etkisi\n",
        "        banyo_factor = banyo_sayisi[i] * 50000\n",
        "        \n",
        "        # Bina yaşı etkisi\n",
        "        age_mapping = {\n",
        "            0: 1.2,  # 0-5 yaş\n",
        "            1: 1.1,  # 5-10 yaş\n",
        "            2: 1.0,  # 10-15 yaş\n",
        "            3: 0.9,  # 15-20 yaş\n",
        "            4: 0.8,  # 20-25 yaş\n",
        "            5: 0.7   # 25+ yaş\n",
        "        }\n",
        "        age_factor = age_mapping[building_age[i]]\n",
        "        \n",
        "        # Isıtma tipi etkisi\n",
        "        heating_mapping = {\n",
        "            0: 1.15,  # Yerden Isıtma\n",
        "            1: 1.05,  # Kombi Doğalgaz\n",
        "            2: 1.0,   # Merkezi Doğalgaz\n",
        "            3: 0.95,  # Klima\n",
        "            4: 0.85,  # Soba\n",
        "            5: 0.9    # Diğer\n",
        "        }\n",
        "        heating_factor = heating_mapping[heating_type[i]]\n",
        "        \n",
        "        # Eşya durumu etkisi\n",
        "        esya_mapping = {\n",
        "            0: 1.1,  # Eşyalı\n",
        "            1: 1.05, # Yarı Eşyalı\n",
        "            2: 1.0   # Boş\n",
        "        }\n",
        "        esya_factor = esya_mapping[esya_durumu[i]]\n",
        "        \n",
        "        # Toplam fiyat\n",
        "        total_price = (base_price + area_factor + room_factor + banyo_factor) * age_factor * heating_factor * esya_factor\n",
        "        \n",
        "        # Rastgele varyasyon ekle\n",
        "        total_price *= np.random.uniform(0.9, 1.1)\n",
        "        \n",
        "        price.append(int(total_price))\n",
        "    \n",
        "    # DataFrame oluştur\n",
        "    df = pd.DataFrame({\n",
        "        'Şehir': city,\n",
        "        'Net_Metrekare': net_area,\n",
        "        'Brüt_Metrekare': gross_area,\n",
        "        'Oda_Sayısı': room_count,\n",
        "        'Banyo_Sayisi': banyo_sayisi,\n",
        "        'Bina_Yaşı': building_age,\n",
        "        'Isıtma_Tipi': heating_type,\n",
        "        'Eşya_Durumu': esya_durumu,\n",
        "        'Fiyat': price\n",
        "    })\n",
        "    \n",
        "    # CSV olarak kaydet\n",
        "    df.to_csv('home_price.csv', index=False)\n",
        "    print(f\"Örnek veri seti oluşturuldu ve kaydedildi. Boyut: {df.shape}\")"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "data_overview"
      },
      "outputs": [],
      "source": [
        "# Veri setine genel bakış\n",
        "print(\"\\n===== VERİ SETİ GENEL BAKIŞ =====\")\n",
        "print(\"\\nİlk 5 satır:\")\n",
        "print(df.head())\n",
        "\n",
        "print(\"\\nVeri seti boyutu:\", df.shape)\n",
        "\n",
        "print(\"\\nVeri tipleri:\")\n",
        "print(df.dtypes)\n",
        "\n",
        "print(\"\\nÖzet istatistikler:\")\n",
        "print(df.describe())\n",
        "\n",
        "print(\"\\nEksik değerler:\")\n",
        "print(df.isnull().sum())"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "data_preprocessing"
      },
      "source": [
        "## 3. Veri Ön İşleme\n",
        "\n",
        "Veri setindeki eksik değerleri dolduralım ve gerekli dönüşümleri yapalım."
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "handle_missing_values"
      },
      "outputs": [],
      "source": [
        "# Eksik değerleri doldurma\n",
        "print(\"\\n===== EKSİK DEĞERLERİ DOLDURMA =====\")\n",
        "\n",
        "# Sayısal değişkenler için medyan ile doldurma\n",
        "numeric_cols = df.select_dtypes(include=[np.number]).columns\n",
        "imputer = SimpleImputer(strategy=\"median\")\n",
        "df[numeric_cols] = imputer.fit_transform(df[numeric_cols])\n",
        "\n",
        "# Kategorik değişkenler için en sık değer ile doldurma\n",
        "categorical_cols = df.select_dtypes(exclude=[np.number]).columns\n",
        "if len(categorical_cols) > 0:\n",
        "    cat_imputer = SimpleImputer(strategy=\"most_frequent\")\n",
        "    df[categorical_cols] = cat_imputer.fit_transform(df[categorical_cols])\n",
        "\n",
        "print(\"Eksik değerler dolduruldu.\")\n",
        "print(\"Kalan eksik değerler:\", df.isnull().sum().sum())"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "feature_engineering"
      },
      "outputs": [],
      "source": [
        "# Özellik mühendisliği\n",
        "print(\"\\n===== ÖZELLİK MÜHENDİSLİĞİ =====\")\n",
        "\n",
        "# m2 başı fiyat\n",
        "df['m2_basi_fiyat'] = df['Fiyat'] / df['Net_Metrekare']\n",
        "\n",
        "# Oda başı m2\n",
        "df['oda_basi_m2'] = df['Net_Metrekare'] / df['Oda_Sayısı']\n",
        "\n",
        "# Banyo başı m2\n",
        "df['banyo_basi_m2'] = df['Net_Metrekare'] / df['Banyo_Sayisi']\n",
        "\n",
        "# Brüt/Net oranı\n",
        "df['brut_net_orani'] = df['Brüt_Metrekare'] / df['Net_Metrekare']\n",
        "\n",
        "# Oda/Banyo oranı\n",
        "df['oda_banyo_orani'] = df['Oda_Sayısı'] / df['Banyo_Sayisi']\n",
        "\n",
        "# Sonsuz ve NaN değerleri temizleme\n",
        "df.replace([np.inf, -np.inf], np.nan, inplace=True)\n",
        "df.fillna(df.median(), inplace=True)\n",
        "\n",
        "print(\"Yeni özellikler eklendi.\")\n",
        "print(\"Güncel veri seti boyutu:\", df.shape)\n",
        "print(\"Yeni özellikler:\", ['m2_basi_fiyat', 'oda_basi_m2', 'banyo_basi_m2', 'brut_net_orani', 'oda_banyo_orani'])"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "eda"
      },
      "source": [
        "## 4. Keşifsel Veri Analizi (EDA)\n",
        "\n",
        "Veri setini daha iyi anlamak için görselleştirmeler yapalım."
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "distribution_analysis"
      },
      "outputs": [],
      "source": [
        "# Fiyat dağılımı\n",
        "plt.figure(figsize=(12, 6))\n",
        "sns.histplot(df['Fiyat'], kde=True)\n",
        "plt.axvline(df['Fiyat'].mean(), color='red', linestyle='--', label=f'Ortalama: {df[\"Fiyat\"].mean():,.0f} TL')\n",
        "plt.axvline(df['Fiyat'].median(), color='green', linestyle='--', label=f'Medyan: {df[\"Fiyat\"].median():,.0f} TL')\n",
        "plt.title('Fiyat Dağılımı')\n",
        "plt.xlabel('Fiyat (TL)')\n",
        "plt.ylabel('Frekans')\n",
        "plt.legend()\n",
        "plt.show()\n",
        "\n",
        "# m2 başı fiyat dağılımı\n",
        "plt.figure(figsize=(12, 6))\n",
        "sns.histplot(df['m2_basi_fiyat'], kde=True)\n",
        "plt.axvline(df['m2_basi_fiyat'].mean(), color='red', linestyle='--', label=f'Ortalama: {df[\"m2_basi_fiyat\"].mean():,.0f} TL')\n",
        "plt.axvline(df['m2_basi_fiyat'].median(), color='green', linestyle='--', label=f'Medyan: {df[\"m2_basi_fiyat\"].median():,.0f} TL')\n",
        "plt.title('m² Başı Fiyat Dağılımı')\n",
        "plt.xlabel('m² Başı Fiyat (TL)')\n",
        "plt.ylabel('Frekans')\n",
        "plt.legend()\n",
        "plt.show()"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "correlation_analysis"
      },
      "outputs": [],
      "source": [
        "# Korelasyon analizi\n",
        "plt.figure(figsize=(14, 10))\n",
        "numeric_df = df.select_dtypes(include=[np.number])\n",
        "corr_matrix = numeric_df.corr()\n",
        "mask = np.triu(np.ones_like(corr_matrix, dtype=bool))\n",
        "sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', fmt='.2f', linewidths=0.5)\n",
        "plt.title('Korelasyon Matrisi')\n",
        "plt.tight_layout()\n",
        "plt.show()\n",
        "\n",
        "# Fiyat ile en yüksek korelasyona sahip değişkenler\n",
        "price_corr = corr_matrix['Fiyat'].sort_values(ascending=False)\n",
        "print(\"\\nFiyat ile en yüksek korelasyona sahip değişkenler:\")\n",
        "print(price_corr)"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "scatter_plots"
      },
      "outputs": [],
      "source": [
        "# Fiyat vs Net Metrekare\n",
        "plt.figure(figsize=(12, 6))\n",
        "sns.scatterplot(x='Net_Metrekare', y='Fiyat', data=df, alpha=0.6, hue='Şehir', palette='viridis')\n",
        "plt.title('Fiyat vs Net Metrekare')\n",
        "plt.xlabel('Net Metrekare')\n",
        "plt.ylabel('Fiyat (TL)')\n",
        "plt.show()\n",
        "\n",
        "# Fiyat vs Oda Sayısı\n",
        "plt.figure(figsize=(12, 6))\n",
        "sns.boxplot(x='Oda_Sayısı', y='Fiyat', data=df)\n",
        "plt.title('Oda Sayısına Göre Fiyat Dağılımı')\n",
        "plt.xlabel('Oda Sayısı')\n",
        "plt.ylabel('Fiyat (TL)')\n",
        "plt.show()\n",
        "\n",
        "# Fiyat vs Bina Yaşı\n",
        "plt.figure(figsize=(12, 6))\n",
        "sns.boxplot(x='Bina_Yaşı', y='Fiyat', data=df)\n",
        "plt.title('Bina Yaşına Göre Fiyat Dağılımı')\n",
        "plt.xlabel('Bina Yaşı')\n",
        "plt.ylabel('Fiyat (TL)')\n",
        "plt.show()"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "categorical_analysis"
      },
      "outputs": [],
      "source": [
        "# Şehirlere göre ortalama fiyat\n",
        "plt.figure(figsize=(14, 6))\n",
        "city_avg_price = df.groupby('Şehir')['Fiyat'].mean().sort_values(ascending=False)\n",
        "sns.barplot(x=city_avg_price.index, y=city_avg_price.values)\n",
        "plt.title('Şehirlere Göre Ortalama Fiyat')\n",
        "plt.xlabel('Şehir Kodu')\n",
        "plt.ylabel('Ortalama Fiyat (TL)')\n",
        "plt.xticks(rotation=0)\n",
        "plt.show()\n",
        "\n",
        "# Isıtma tipine göre ortalama fiyat\n",
        "plt.figure(figsize=(14, 6))\n",
        "heating_avg_price = df.groupby('Isıtma_Tipi')['Fiyat'].mean().sort_values(ascending=False)\n",
        "sns.barplot(x=heating_avg_price.index, y=heating_avg_price.values)\n",
        "plt.title('Isıtma Tipine Göre Ortalama Fiyat')\n",
        "plt.xlabel('Isıtma Tipi Kodu')\n",
        "plt.ylabel('Ortalama Fiyat (TL)')\n",
        "plt.xticks(rotation=0)\n",
        "plt.show()"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "prepare_data_for_ml"
      },
      "source": [
        "## 5. Makine Öğrenmesi için Veri Hazırlama\n",
        "\n",
        "Veriyi eğitim ve test setlerine ayıralım ve ölçeklendirelim."
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "prepare_features"
      },
      "outputs": [],
      "source": [
        "# Özellikler ve hedef değişkenleri hazırlama\n",
        "print(\"\\n===== MAKİNE ÖĞRENMESİ İÇİN VERİ HAZIRLAMA =====\")\n",
        "\n",
        "# Regresyon için hedef değişken (Fiyat)\n",
        "y_reg = df['Fiyat']\n",
        "\n",
        "# Sınıflandırma için hedef değişken (Fiyat medyanın üzerinde mi?)\n",
        "threshold = df['Fiyat'].median()\n",
        "y_class = (df['Fiyat'] > threshold).astype(int)\n",
        "print(f\"Sınıflandırma için eşik değeri: {threshold:,.0f} TL\")\n",
        "print(f\"Sınıf dağılımı: 0 (Düşük): {(y_class==0).sum()}, 1 (Yüksek): {(y_class==1).sum()}\")\n",
        "\n",
        "# Özellikler\n",
        "feature_cols = ['Net_Metrekare', 'Brüt_Metrekare', 'Oda_Sayısı', 'Banyo_Sayisi', \n",
        "                'Bina_Yaşı', 'Isıtma_Tipi', 'Eşya_Durumu', 'Şehir',\n",
        "                'oda_basi_m2', 'banyo_basi_m2', 'brut_