const axios = require('axios');

async function testPriceAPI() {
    try {
        console.log('🔍 Fiyat tahmini test başlıyor...');
        
        const priceData = {
            Net_Metrekare: "120",
            Oda_Sayısı: 3,
            <PERSON><PERSON><PERSON>: "istanbul",
            Binanın_Yaşı: "1-5",
            <PERSON><PERSON><PERSON>_Durumu: "Boş",
            Isıtma_Tipi: "Kombi Doğalgaz",
            Bulunduğu_Kat: "2.Kat",
            Binanın_Kat_Sayısı: "5",
            Banyo_Sayısı: 2
        };
        
        console.log('📋 Gönderilen data:', priceData);
        
        const priceResponse = await axios.post('http://localhost:3001/api/predict-price', priceData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        
        console.log('✅ Price prediction status:', priceResponse.status);
        console.log('✅ Price prediction data:', JSON.stringify(priceResponse.data, null, 2));
        
    } catch (error) {
        console.error('❌ Test hatası:', error.message);
        if (error.response) {
            console.error('❌ Response status:', error.response.status);
            console.error('❌ Response data:', error.response.data);
        }
    }
}

testPriceAPI();
