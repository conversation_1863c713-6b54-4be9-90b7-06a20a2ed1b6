#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tez için eksik grafikleri oluşturma scripti
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer
from sklearn.pipeline import Pipeline
import warnings
warnings.filterwarnings('ignore')

def create_regression_graphs():
    """Regresyon performans grafikleri oluştur"""
    print("Regresyon grafikleri oluşturuluyor...")
    
    # Veri yükleme
    df = pd.read_csv('demet tez/gayrimenkul-karar-destek-sistemi/data/home_price.csv')
    df = df[df['Fiyat'] > 0]
    df = df[df['Net_Metrekare'] > 0]
    
    # Özellik mühendisliği
    df['Fiyat_Per_M2'] = df['Fiyat'] / df['Net_Metrekare']
    
    # Özellik listesi
    numeric_features = ['Net_Metrekare', 'Fiyat_Per_M2', 'Oda_Sayısı', 'Banyo_Sayısı']
    categorical_features = ['Şehir']
    
    # Mevcut sütunları kontrol et
    numeric_features = [col for col in numeric_features if col in df.columns]
    categorical_features = [col for col in categorical_features if col in df.columns]
    
    # Preprocessing
    numeric_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='median')),
        ('scaler', StandardScaler())
    ])
    
    categorical_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='most_frequent')),
        ('onehot', OneHotEncoder(handle_unknown='ignore', drop='first'))
    ])
    
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', numeric_transformer, numeric_features),
            ('cat', categorical_transformer, categorical_features)
        ])
    
    # Veri hazırlama
    X = df[numeric_features + categorical_features].copy()
    y = df['Fiyat']
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    X_train_processed = preprocessor.fit_transform(X_train)
    X_test_processed = preprocessor.transform(X_test)
    
    if hasattr(X_train_processed, 'toarray'):
        X_train_processed = X_train_processed.toarray()
    if hasattr(X_test_processed, 'toarray'):
        X_test_processed = X_test_processed.toarray()
    
    # Modelleri eğit
    rf_regressor = RandomForestRegressor(n_estimators=50, random_state=42)
    rf_regressor.fit(X_train_processed, y_train)
    rf_pred = rf_regressor.predict(X_test_processed)
    rf_r2 = r2_score(y_test, rf_pred)
    rf_rmse = np.sqrt(mean_squared_error(y_test, rf_pred))
    
    lr_regressor = LinearRegression()
    lr_regressor.fit(X_train_processed, y_train)
    lr_pred = lr_regressor.predict(X_test_processed)
    lr_r2 = r2_score(y_test, lr_pred)
    lr_rmse = np.sqrt(mean_squared_error(y_test, lr_pred))
    
    # Regresyon performans grafiği
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Regresyon Modelleri Performans Analizi', fontsize=16, fontweight='bold')
    
    # R² skorları
    models = ['Random Forest', 'Linear Regression']
    r2_scores = [rf_r2, lr_r2]
    bars1 = axes[0,0].bar(models, r2_scores, color=['blue', 'red'], alpha=0.7)
    axes[0,0].set_title('R² Skorları Karşılaştırması')
    axes[0,0].set_ylabel('R² Skoru')
    axes[0,0].set_ylim(0, 1)
    for bar, score in zip(bars1, r2_scores):
        height = bar.get_height()
        axes[0,0].text(bar.get_x() + bar.get_width()/2., height + 0.01, 
                      f'{score:.3f}', ha='center', va='bottom')
    
    # RMSE
    rmse_scores = [rf_rmse, lr_rmse]
    bars2 = axes[0,1].bar(models, rmse_scores, color=['blue', 'red'], alpha=0.7)
    axes[0,1].set_title('RMSE Karşılaştırması')
    axes[0,1].set_ylabel('RMSE')
    for bar, score in zip(bars2, rmse_scores):
        height = bar.get_height()
        axes[0,1].text(bar.get_x() + bar.get_width()/2., height + height*0.01, 
                      f'{score:,.0f}', ha='center', va='bottom', fontsize=8)
    
    # Gerçek vs Tahmin (Random Forest)
    axes[1,0].scatter(y_test, rf_pred, alpha=0.5, s=10)
    axes[1,0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    axes[1,0].set_xlabel('Gerçek Fiyat')
    axes[1,0].set_ylabel('Tahmin Edilen Fiyat')
    axes[1,0].set_title(f'Random Forest: Gerçek vs Tahmin (R²={rf_r2:.3f})')
    
    # Hata dağılımı
    residuals = y_test - rf_pred
    axes[1,1].hist(residuals, bins=50, alpha=0.7, color='green')
    axes[1,1].set_xlabel('Hata (Gerçek - Tahmin)')
    axes[1,1].set_ylabel('Frekans')
    axes[1,1].set_title('Random Forest Hata Dağılımı')
    axes[1,1].axvline(x=0, color='red', linestyle='--')
    
    plt.tight_layout()
    plt.savefig('regresyon_performans_grafigi.png', dpi=300, bbox_inches='tight')
    print('regresyon_performans_grafigi.png kaydedildi')
    plt.close()
    
    # Özellik önem grafiği
    plt.figure(figsize=(10, 6))
    feature_importance = rf_regressor.feature_importances_
    n_features = len(feature_importance)
    feature_names = numeric_features + [f'Şehir_{i}' for i in range(n_features - len(numeric_features))]
    if len(feature_names) > n_features:
        feature_names = feature_names[:n_features]
    
    importance_df = pd.DataFrame({
        'Feature': feature_names,
        'Importance': feature_importance
    }).sort_values('Importance', ascending=False).head(10)
    
    plt.barh(range(len(importance_df)), importance_df['Importance'], alpha=0.7, color='skyblue')
    plt.yticks(range(len(importance_df)), importance_df['Feature'])
    plt.xlabel('Önem Skoru')
    plt.title('Random Forest Özellik Önem Analizi (Top 10)')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.savefig('ozellik_onem_grafigi.png', dpi=300, bbox_inches='tight')
    print('ozellik_onem_grafigi.png kaydedildi')
    plt.close()

def create_investment_graphs():
    """Yatırım uygunluk analizi grafikleri oluştur"""
    print("Yatırım analizi grafikleri oluşturuluyor...")
    
    # Veri yükleme
    df = pd.read_csv('demet tez/gayrimenkul-karar-destek-sistemi/data/home_price.csv')
    df = df[df['Fiyat'] > 0]
    df = df[df['Net_Metrekare'] > 0]
    
    # Özellik mühendisliği
    df['Fiyat_Per_M2'] = df['Fiyat'] / df['Net_Metrekare']
    
    def calculate_investment_score(row):
        score = 0
        
        # Fiyat/m² faktörü (30% ağırlık)
        price_per_m2 = row['Fiyat_Per_M2']
        city_avg_price = df[df['Şehir'] == row['Şehir']]['Fiyat_Per_M2'].median()
        
        if price_per_m2 <= city_avg_price * 0.8:
            price_score = 9
        elif price_per_m2 <= city_avg_price * 0.9:
            price_score = 7
        elif price_per_m2 <= city_avg_price * 1.1:
            price_score = 5
        elif price_per_m2 <= city_avg_price * 1.3:
            price_score = 3
        else:
            price_score = 1
        
        score += price_score * 0.3
        
        # Alan faktörü (20% ağırlık)
        area = row['Net_Metrekare']
        if area >= 150:
            area_score = 9
        elif area >= 120:
            area_score = 7
        elif area >= 90:
            area_score = 5
        elif area >= 60:
            area_score = 3
        else:
            area_score = 1
        
        score += area_score * 0.2
        
        # Oda sayısı faktörü (15% ağırlık)
        rooms = row.get('Oda_Sayısı', 3)
        if pd.isna(rooms):
            rooms = 3
        if rooms >= 5:
            room_score = 9
        elif rooms >= 4:
            room_score = 7
        elif rooms >= 3:
            room_score = 5
        elif rooms >= 2:
            room_score = 3
        else:
            room_score = 1
        
        score += room_score * 0.15
        
        # Şehir faktörü (15% ağırlık)
        city = str(row['Şehir']).lower()
        if city in ['istanbul', 'ankara', 'izmir']:
            city_score = 9
        elif city in ['antalya', 'bursa', 'adana']:
            city_score = 7
        else:
            city_score = 5
        
        score += city_score * 0.15
        
        # Basit faktörler (20% ağırlık)
        score += 5 * 0.2
        
        return score
    
    df['Yatirim_Skoru'] = df.apply(calculate_investment_score, axis=1)
    
    # 9 kategoriye ayır
    score_bins = [0, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    score_labels = ['Çok Pahalı', 'Pahalı', 'Kısmen Pahalı', 'Belirsiz', 
                   'Makul Fiyat', 'Kısmen Uygun', 'Uygun', 'Çok Uygun', 'Mükemmel']
    
    df['Yatirim_Kategori'] = pd.cut(df['Yatirim_Skoru'], 
                                   bins=score_bins, 
                                   labels=score_labels, 
                                   include_lowest=True)
    
    # İstatistikler
    category_counts = df['Yatirim_Kategori'].value_counts()
    city_investment = df.groupby('Şehir')['Yatirim_Skoru'].mean().sort_values(ascending=False).head(10)
    
    # Görselleştirme
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Çok Faktörlü Yatırım Uygunluk Analizi', fontsize=16, fontweight='bold')
    
    # Yatırım kategorileri dağılımı (pasta grafiği)
    axes[0,0].pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%', startangle=90)
    axes[0,0].set_title('Yatırım Uygunluk Kategorileri Dağılımı')
    
    # Yatırım skoru dağılımı (histogram)
    mean_score = df['Yatirim_Skoru'].mean()
    axes[0,1].hist(df['Yatirim_Skoru'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,1].set_title('Yatırım Skoru Dağılımı')
    axes[0,1].set_xlabel('Yatırım Skoru')
    axes[0,1].set_ylabel('Frekans')
    axes[0,1].axvline(mean_score, color='red', linestyle='--', label=f'Ortalama: {mean_score:.2f}')
    axes[0,1].legend()
    
    # Şehirlere göre ortalama yatırım skoru (bar grafiği)
    axes[1,0].bar(range(len(city_investment)), city_investment.values, color='green', alpha=0.7)
    axes[1,0].set_title('Şehirlere Göre Ortalama Yatırım Skoru (Top 10)')
    axes[1,0].set_xlabel('Şehirler')
    axes[1,0].set_ylabel('Ortalama Yatırım Skoru')
    axes[1,0].set_xticks(range(len(city_investment)))
    axes[1,0].set_xticklabels(city_investment.index, rotation=45)
    
    # Fiyat vs Yatırım Skoru scatter plot
    scatter = axes[1,1].scatter(df['Fiyat'], df['Yatirim_Skoru'], 
                               c=df['Yatirim_Skoru'], cmap='RdYlGn', alpha=0.6, s=10)
    axes[1,1].set_title('Fiyat vs Yatırım Skoru İlişkisi')
    axes[1,1].set_xlabel('Fiyat (TL)')
    axes[1,1].set_ylabel('Yatırım Skoru')
    axes[1,1].ticklabel_format(style='plain', axis='x')
    plt.colorbar(scatter, ax=axes[1,1])
    
    plt.tight_layout()
    plt.savefig('yatirim_uygunluk_analizi.png', dpi=300, bbox_inches='tight')
    print('yatirim_uygunluk_analizi.png kaydedildi')
    plt.close()
    
    print(f'Ortalama yatırım skoru: {mean_score:.2f}')
    print(f'En yüksek skor: {df["Yatirim_Skoru"].max():.2f}')
    print(f'En düşük skor: {df["Yatirim_Skoru"].min():.2f}')

if __name__ == "__main__":
    print("Tez grafikleri oluşturuluyor...")
    create_regression_graphs()
    create_investment_graphs()
    print("Tüm grafikler oluşturuldu!")
