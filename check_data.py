import pandas as pd

try:
    df = pd.read_csv('home_price.csv')
    print("✅ Veri dosyası başarıyla yüklendi")
    print(f"📊 Satır sayısı: {len(df)}")
    print(f"📋 Sütun sayısı: {len(df.columns)}")
    print("\n🏷️ Sütun isimleri:")
    for i, col in enumerate(df.columns):
        print(f"   {i+1}. {col}")
    
    print("\n📝 İlk 3 satır:")
    print(df.head(3))
    
    print("\n🔍 Veri tipleri:")
    print(df.dtypes)
    
except Exception as e:
    print(f"❌ Hata: {e}")
