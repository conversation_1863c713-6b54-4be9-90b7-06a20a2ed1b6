/**
 * 🚀 TAM KAPASİTE Gayrimenkul Karar Destek Sistemi Backend Server
 * Optimized for maximum performance and unlimited data processing
 * Tezde bahsedilen Node.js API servisleri - TAM KAPASİTE MODU
 */

const express = require('express');
const cors = require('cors');
const axios = require('axios');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;
const ML_API_URL = process.env.ML_API_URL || 'http://localhost:5000'; // TAM KAPASİTE ML API

// TAM KAPASİTE PERFORMANS AYARLARI
const FULL_CAPACITY_MODE = process.env.FULL_CAPACITY_MODE !== 'false';
const MAX_REQUEST_SIZE = FULL_CAPACITY_MODE ? '100mb' : '10mb';
const REQUEST_TIMEOUT = FULL_CAPACITY_MODE ? 120000 : 30000; // 2 dakika vs 30 saniye

console.log(`🚀 TAM KAPASİTE MODU: ${FULL_CAPACITY_MODE ? 'AKTİF' : 'DEVRE DIŞI'}`);
console.log(`📊 Max Request Size: ${MAX_REQUEST_SIZE}`);
console.log(`⏱️ Request Timeout: ${REQUEST_TIMEOUT}ms`);

// NaN değerlerini temizleme fonksiyonu
function cleanNaNValues(obj) {
    if (obj === null || obj === undefined) return null;

    if (typeof obj === 'number' && (isNaN(obj) || !isFinite(obj))) {
        return null;
    }

    if (Array.isArray(obj)) {
        return obj.map(cleanNaNValues);
    }

    if (typeof obj === 'object') {
        const cleaned = {};
        for (const [key, value] of Object.entries(obj)) {
            cleaned[key] = cleanNaNValues(value);
        }
        return cleaned;
    }

    return obj;
}

// TAM KAPASİTE Middleware
app.use(helmet({
    contentSecurityPolicy: FULL_CAPACITY_MODE ? false : undefined // TAM KAPASİTE modunda CSP devre dışı
}));

app.use(compression({
    level: FULL_CAPACITY_MODE ? 9 : 6, // TAM KAPASİTE modunda maksimum sıkıştırma
    threshold: FULL_CAPACITY_MODE ? 0 : 1024 // TAM KAPASİTE modunda her şeyi sıkıştır
}));

app.use(morgan(FULL_CAPACITY_MODE ? 'combined' : 'dev'));

app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
    maxAge: FULL_CAPACITY_MODE ? 86400 : 3600 // TAM KAPASİTE modunda daha uzun cache
}));

app.use(express.json({
    limit: MAX_REQUEST_SIZE,
    parameterLimit: FULL_CAPACITY_MODE ? 100000 : 1000 // TAM KAPASİTE modunda daha fazla parametre
}));

app.use(express.urlencoded({
    extended: true,
    limit: MAX_REQUEST_SIZE,
    parameterLimit: FULL_CAPACITY_MODE ? 100000 : 1000
}));

// Sağlık kontrolü
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Gayrimenkul Backend API'
    });
});

// TAM KAPASİTE ML API proxy fonksiyonu
async function callMLAPI(endpoint, data = null, method = 'GET') {
    try {
        const config = {
            method,
            url: `${ML_API_URL}${endpoint}`,
            timeout: REQUEST_TIMEOUT, // TAM KAPASİTE timeout
            headers: {
                'Content-Type': 'application/json',
                'X-Full-Capacity-Mode': FULL_CAPACITY_MODE.toString(),
                'User-Agent': 'Gayrimenkul-Backend-TAM-KAPASITE/1.0'
            },
            maxContentLength: FULL_CAPACITY_MODE ? Infinity : 50 * 1024 * 1024, // TAM KAPASİTE: sınırsız
            maxBodyLength: FULL_CAPACITY_MODE ? Infinity : 50 * 1024 * 1024
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            config.data = data;
        }

        console.log(`🚀 TAM KAPASİTE ML API çağrısı: ${method} ${endpoint}`);
        const startTime = Date.now();

        const response = await axios(config);

        const duration = Date.now() - startTime;
        console.log(`✅ ML API yanıtı alındı: ${duration}ms`);

        // Response tipini kontrol et

        // Eğer string ise parse et
        if (typeof response.data === 'string') {
            try {
                // NaN değerlerini null olarak değiştir
                const cleanedData = response.data.replace(/: NaN/g, ': null')
                    .replace(/NaN/g, 'null')
                    .replace(/: undefined/g, ': null');
                return JSON.parse(cleanedData);
            } catch (parseError) {
                console.error('JSON parse error:', parseError);
                // Hata durumunda orijinal veriyi döndür
                return response.data;
            }
        }

        // Object ise NaN değerlerini temizle
        if (typeof response.data === 'object' && response.data !== null) {
            return cleanNaNValues(response.data);
        }

        return response.data;
    } catch (error) {
        console.error(`ML API Error (${endpoint}):`, error.message);
        throw new Error(`ML servisine erişim hatası: ${error.message}`);
    }
}

// Fiyat tahmini endpoint'i
app.post('/api/predict-price', async (req, res) => {
    try {
        let propertyData = req.body;

        // Alan adı eşleştirmesi (Türkçe karaktersiz -> Türkçe karakterli)
        const fieldMapping = {
            'Oda_Sayisi': 'Oda_Sayısı',
            'Sehir': 'Şehir'
        };

        // Alan adlarını normalize et
        const normalizedData = {};
        for (const [key, value] of Object.entries(propertyData)) {
            const normalizedKey = fieldMapping[key] || key;
            normalizedData[normalizedKey] = value;
        }

        propertyData = normalizedData;

        // Veri validasyonu
        const requiredFields = ['Net_Metrekare', 'Oda_Sayısı', 'Şehir'];
        const missingFields = requiredFields.filter(field => !propertyData[field]);

        if (missingFields.length > 0) {
            return res.status(400).json({
                error: 'Eksik alanlar',
                missing_fields: missingFields
            });
        }

        // Debug: Gönderilecek veriyi logla
        console.log('ML API\'ye gönderilecek veri:', JSON.stringify(propertyData, null, 2));

        // ML API'sini çağır
        const result = await callMLAPI('/api/predict-price', propertyData, 'POST');

        // ML API yanıtı alındı
        console.log('ML API Response:', JSON.stringify(result, null, 2));

        // Sonucu formatla
        const response = {
            success: true,
            data: {
                predicted_price: result.predicted_price ? Math.round(result.predicted_price) : null,
                price_class: {
                    class: result.price_class ? result.price_class.toLowerCase().replace('ü', 'u').replace('ş', 's') : 'orta',
                    label: result.price_class || 'Orta'
                },
                similar_properties: result.similar_properties || [],
                investment_recommendation: result.investment_recommendation || {
                    recommendation: 'Değerlendirme yapılamadı',
                    score: 5,
                    city_average: 0,
                    price_ratio: 1.0
                },
                analysis_date: new Date().toISOString()
            }
        };

        res.json(response);

    } catch (error) {
        console.warn('⚠️ ML API fiyat tahmini başarısız, fallback hesaplama kullanılıyor:', error.message);

        try {
            // Fallback fiyat tahmini - Basit hesaplama
            const area = parseInt(propertyData.Net_Metrekare) || 100;
            const rooms = parseInt(propertyData.Oda_Sayısı) || 3;
            const city = propertyData.Şehir?.toLowerCase() || 'ankara';

        // Şehir bazlı m² fiyatları
        const cityPrices = {
            'istanbul': 35000,
            'ankara': 18000,
            'izmir': 23000,
            'bursa': 15000,
            'antalya': 25000,
            'adana': 12000,
            'gaziantep': 10000,
            'konya': 9000,
            'kayseri': 8000,
            'eskisehir': 11000
        };

        const basePricePerSqm = cityPrices[city] || 12000;

        // Oda sayısı çarpanı
        const roomMultiplier = 1 + (rooms - 3) * 0.1;

        // Yaş çarpanı
        const ageMultiplier = propertyData.Binanın_Yaşı?.includes('0') ? 1.2 :
                             propertyData.Binanın_Yaşı?.includes('1-5') ? 1.1 :
                             propertyData.Binanın_Yaşı?.includes('5-10') ? 1.0 : 0.9;

        const estimatedPrice = Math.round(area * basePricePerSqm * roomMultiplier * ageMultiplier);

        // Yatırım uygunluk kategorisi
        const pricePerSqm = estimatedPrice / area;
        let investmentCategory = 'Orta Seviye';
        if (pricePerSqm < 10000) investmentCategory = 'Çok Uygun';
        else if (pricePerSqm < 15000) investmentCategory = 'Uygun';
        else if (pricePerSqm < 20000) investmentCategory = 'Makul Fiyat';
        else if (pricePerSqm < 25000) investmentCategory = 'Kısmen Uygun';
        else if (pricePerSqm < 30000) investmentCategory = 'Orta Seviye';
        else if (pricePerSqm < 35000) investmentCategory = 'Değerlendirilmeli';
        else if (pricePerSqm < 40000) investmentCategory = 'Uygun Değil';
        else if (pricePerSqm < 50000) investmentCategory = 'Pahalı';
        else investmentCategory = 'Çok Pahalı';

        const fallbackResponse = {
            success: true,
            data: {
                predicted_price: estimatedPrice,
                price_class: {
                    class: investmentCategory.toLowerCase().replace(/ü/g, 'u').replace(/ş/g, 's').replace(/ç/g, 'c').replace(/ğ/g, 'g').replace(/ı/g, 'i').replace(/ö/g, 'o'),
                    label: investmentCategory
                },
                similar_properties: [],
                investment_recommendation: {
                    recommendation: investmentCategory,
                    score: pricePerSqm < 20000 ? 8 : pricePerSqm < 30000 ? 6 : 4,
                    city_average: basePricePerSqm,
                    price_ratio: pricePerSqm / basePricePerSqm
                },
                analysis_date: new Date().toISOString(),
                note: 'Offline hesaplama kullanılıyor - Basit fiyat tahmini'
            }
        };

            res.json(fallbackResponse);
        } catch (fallbackError) {
            console.error('❌ Fallback fiyat tahmini hatası:', fallbackError);
            res.status(500).json({
                success: false,
                error: 'Fiyat tahmini yapılamadı',
                message: fallbackError.message
            });
        }
    }
});

// Gayrimenkul filtreleme endpoint'i - İYİLEŞTİRİLMİŞ
app.post('/api/filter-properties', async (req, res) => {
    try {
        console.log('🚀 POST /api/filter-properties endpoint çağrıldı!');
        console.log('📋 Request headers:', req.headers);
        console.log('📋 Request body:', req.body);

        const filters = req.body;
        console.log('🔍 Filtreleme isteği alındı:', filters);

        try {
            // ML API'sini çağır
            const result = await callMLAPI('/api/filter-properties', filters, 'POST');
            console.log('✅ ML API yanıtı alındı');

            const response = {
                success: true,
                properties: result.properties || result.data?.properties || [],
                total_count: result.total_count || result.data?.total_count || 0,
                pagination: result.pagination || result.data?.pagination || {
                    current_page: filters.page || 1,
                    total_pages: Math.ceil((result.total_count || 0) / (filters.limit || 20)),
                    page_size: filters.limit || 20,
                    has_next: false,
                    has_prev: false
                },
                filters_applied: filters
            };

            res.json(response);

        } catch (mlError) {
            console.warn('⚠️ ML API çağrısı başarısız, fallback veri kullanılıyor:', mlError.message);

            // Fallback: Örnek gayrimenkul verileri - 9 KATEGORİLİ YATIRIM UYGUNLUK
            const allSampleProperties = [];
            const categories = [
                { name: 'Çok Uygun', pricePerSqm: 7000 },
                { name: 'Uygun', pricePerSqm: 10000 },
                { name: 'Makul Fiyat', pricePerSqm: 13000 },
                { name: 'Kısmen Uygun', pricePerSqm: 16000 },
                { name: 'Orta Seviye', pricePerSqm: 20000 },
                { name: 'Değerlendirilmeli', pricePerSqm: 25000 },
                { name: 'Uygun Değil', pricePerSqm: 32000 },
                { name: 'Pahalı', pricePerSqm: 40000 },
                { name: 'Çok Pahalı', pricePerSqm: 50000 }
            ];

            const cities = ['adana', 'adiyaman', 'afyonkarahisar', 'agri', 'aksaray', 'amasya', 'ankara', 'antalya', 'ardahan', 'artvin', 'aydin', 'balikesir', 'bartin', 'batman', 'bayburt', 'bilecik', 'bingol', 'bitlis', 'bolu', 'burdur', 'bursa', 'canakkale', 'cankiri', 'corum', 'denizli', 'diyarbakir', 'duzce', 'edirne', 'elazig', 'erzincan', 'erzurum', 'eskisehir', 'gaziantep', 'giresun', 'gumushane', 'hakkari', 'hatay', 'igdir', 'isparta', 'istanbul', 'izmir', 'kahramanmaras', 'karabuk', 'karaman', 'kars', 'kastamonu', 'kayseri', 'kirikkale', 'kirklareli', 'kirsehir', 'kilis', 'kocaeli', 'konya', 'kutahya', 'malatya', 'manisa', 'mardin', 'mersin', 'mugla', 'mus', 'nevsehir', 'nigde', 'ordu', 'osmaniye', 'rize', 'sakarya', 'samsun', 'siirt', 'sinop', 'sivas', 'sanliurfa', 'sirnak', 'tekirdag', 'tokat', 'trabzon', 'tunceli', 'usak', 'van', 'yalova', 'yozgat', 'zonguldak'];

            // Daha fazla örnek veri oluştur (100 ilan)
            for (let i = 0; i < 100; i++) {
                const category = categories[i % categories.length];
                const area = 60 + (i * 2); // 60-260 m² arası
                const price = category.pricePerSqm * area + (Math.random() * 100000 - 50000); // Biraz rastgelelik
                const city = cities[i % cities.length];

                allSampleProperties.push({
                    id: i + 1,
                    Net_Metrekare: area,
                    Oda_Sayısı: 1 + (i % 5), // 1-5 oda
                    Fiyat: Math.round(price),
                    Şehir: city,
                    Yatırıma_Uygunluk: category.name,
                    Eşya_Durumu: i % 2 === 0 ? 'Boş' : 'Eşyalı',
                    Binanın_Yaşı: ['0 (Yeni)', '1-5', '5-10', '11-15', '16-20', '21+'][i % 6],
                    Bulunduğu_Kat: String((i % 15) + 1) + '.Kat',
                    Isıtma_Tipi: ['Kombi Doğalgaz', 'Merkezi Doğalgaz', 'Klimalı', 'Soba'][i % 4],
                    Banyo_Sayısı: 1 + (i % 3), // 1-3 banyo
                    Binanın_Kat_Sayısı: 3 + (i % 8), // 3-10 kat
                    m2_basi_fiyat: Math.round(price / area)
                });
            }

            console.log(`📊 Fallback: ${allSampleProperties.length} örnek ilan oluşturuldu`);

            // FİLTRELEME MANTIGI UYGULA
            let filteredProperties = allSampleProperties;

            // Şehir filtresi
            if (filters.city && filters.city.trim() !== '') {
                filteredProperties = filteredProperties.filter(prop =>
                    prop.Şehir.toLowerCase() === filters.city.toLowerCase()
                );
                console.log(`🏙️ Şehir filtresi (${filters.city}): ${filteredProperties.length} ilan`);
            }

            // Fiyat filtresi
            if (filters.min_price) {
                filteredProperties = filteredProperties.filter(prop =>
                    prop.Fiyat >= parseInt(filters.min_price)
                );
                console.log(`💰 Min fiyat filtresi (${filters.min_price}): ${filteredProperties.length} ilan`);
            }

            if (filters.max_price) {
                filteredProperties = filteredProperties.filter(prop =>
                    prop.Fiyat <= parseInt(filters.max_price)
                );
                console.log(`💰 Max fiyat filtresi (${filters.max_price}): ${filteredProperties.length} ilan`);
            }

            // Oda sayısı filtresi
            if (filters.min_rooms) {
                filteredProperties = filteredProperties.filter(prop =>
                    prop.Oda_Sayısı >= parseInt(filters.min_rooms)
                );
                console.log(`🏠 Min oda filtresi (${filters.min_rooms}): ${filteredProperties.length} ilan`);
            }

            if (filters.max_rooms) {
                filteredProperties = filteredProperties.filter(prop =>
                    prop.Oda_Sayısı <= parseInt(filters.max_rooms)
                );
                console.log(`🏠 Max oda filtresi (${filters.max_rooms}): ${filteredProperties.length} ilan`);
            }

            // Alan filtresi
            if (filters.min_area) {
                filteredProperties = filteredProperties.filter(prop =>
                    prop.Net_Metrekare >= parseInt(filters.min_area)
                );
                console.log(`📐 Min alan filtresi (${filters.min_area}): ${filteredProperties.length} ilan`);
            }

            if (filters.max_area) {
                filteredProperties = filteredProperties.filter(prop =>
                    prop.Net_Metrekare <= parseInt(filters.max_area)
                );
                console.log(`📐 Max alan filtresi (${filters.max_area}): ${filteredProperties.length} ilan`);
            }

            // Yatırım uygunluk filtresi
            if (filters.investment_suitability && filters.investment_suitability.trim() !== '') {
                filteredProperties = filteredProperties.filter(prop =>
                    prop.Yatırıma_Uygunluk === filters.investment_suitability
                );
                console.log(`📊 Yatırım uygunluk filtresi (${filters.investment_suitability}): ${filteredProperties.length} ilan`);
            }

            // Sayfalama uygula
            const page = parseInt(filters.page) || 1;
            const limit = parseInt(filters.limit) || 20;
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedProperties = filteredProperties.slice(startIndex, endIndex);

            console.log(`📄 Sayfalama: Sayfa ${page}, Limit ${limit}, Gösterilen: ${paginatedProperties.length}/${filteredProperties.length}`);

            const totalPages = Math.ceil(filteredProperties.length / limit);

            const sampleProperties = paginatedProperties;

            const response = {
                success: true,
                properties: sampleProperties,
                total_count: filteredProperties.length,
                pagination: {
                    current_page: page,
                    total_pages: totalPages,
                    page_size: limit,
                    has_next: page < totalPages,
                    has_prev: page > 1
                },
                filters_applied: filters,
                note: 'Offline örnek veri kullanılıyor - Filtreleme aktif'
            };

            res.json(response);
        }

    } catch (error) {
        console.error('❌ Filtreleme hatası:', error);
        res.status(500).json({
            success: false,
            error: 'Filtreleme yapılırken hata oluştu',
            message: error.message
        });
    }
});

// Piyasa analizi endpoint'i
app.get('/api/market-analysis', async (req, res) => {
    try {
        const result = await callMLAPI('/api/market-analysis');

        const response = {
            success: true,
            data: {
                city_analysis: result.city_analysis,
                room_analysis: result.room_analysis,
                general_stats: result.general_stats,
                model_performance: result.model_performance,
                analysis_date: new Date().toISOString()
            }
        };

        res.json(response);

    } catch (error) {
        console.warn('⚠️ ML API piyasa analizi başarısız, fallback veri kullanılıyor:', error.message);

        // Fallback piyasa analizi verisi
        const fallbackMarketData = {
            success: true,
            data: {
                general_stats: {
                    total_properties: 19920,
                    avg_price: 2500000,
                    avg_area: 120,
                    avg_rooms: 3.2,
                    avg_price_per_sqm: 20833
                },
                city_analysis: {
                    istanbul: { avg_price: 4500000, count: 5000, avg_price_per_sqm: 37500 },
                    ankara: { avg_price: 2200000, count: 2500, avg_price_per_sqm: 18333 },
                    izmir: { avg_price: 2800000, count: 2000, avg_price_per_sqm: 23333 },
                    bursa: { avg_price: 1800000, count: 1500, avg_price_per_sqm: 15000 },
                    antalya: { avg_price: 3200000, count: 1200, avg_price_per_sqm: 26667 }
                },
                room_analysis: {
                    '1': { avg_price: 1500000, count: 2000 },
                    '2': { avg_price: 2200000, count: 4500 },
                    '3': { avg_price: 2800000, count: 6000 },
                    '4': { avg_price: 3500000, count: 4000 },
                    '5': { avg_price: 4200000, count: 2000 }
                },
                model_performance: {
                    accuracy: 0.85,
                    r2_score: 0.82,
                    mae: 250000,
                    rmse: 380000
                },
                analysis_date: new Date().toISOString(),
                note: 'Offline örnek piyasa verisi kullanılıyor'
            }
        };

        res.json(fallbackMarketData);
    }
});

// Şehir listesi endpoint'i - TÜM ŞEHİRLER
app.get('/api/cities', (req, res) => {
    const cities = [
        { value: 'adana', label: 'Adana', region: 'Akdeniz' },
        { value: 'adiyaman', label: 'Adıyaman', region: 'Güneydoğu Anadolu' },
        { value: 'afyonkarahisar', label: 'Afyonkarahisar', region: 'Ege' },
        { value: 'agri', label: 'Ağrı', region: 'Doğu Anadolu' },
        { value: 'aksaray', label: 'Aksaray', region: 'İç Anadolu' },
        { value: 'amasya', label: 'Amasya', region: 'Karadeniz' },
        { value: 'ankara', label: 'Ankara', region: 'İç Anadolu' },
        { value: 'antalya', label: 'Antalya', region: 'Akdeniz' },
        { value: 'ardahan', label: 'Ardahan', region: 'Doğu Anadolu' },
        { value: 'artvin', label: 'Artvin', region: 'Karadeniz' },
        { value: 'aydin', label: 'Aydın', region: 'Ege' },
        { value: 'balikesir', label: 'Balıkesir', region: 'Marmara' },
        { value: 'bartin', label: 'Bartın', region: 'Karadeniz' },
        { value: 'batman', label: 'Batman', region: 'Güneydoğu Anadolu' },
        { value: 'bayburt', label: 'Bayburt', region: 'Karadeniz' },
        { value: 'bilecik', label: 'Bilecik', region: 'Marmara' },
        { value: 'bingol', label: 'Bingöl', region: 'Doğu Anadolu' },
        { value: 'bitlis', label: 'Bitlis', region: 'Doğu Anadolu' },
        { value: 'bolu', label: 'Bolu', region: 'Karadeniz' },
        { value: 'burdur', label: 'Burdur', region: 'Akdeniz' },
        { value: 'bursa', label: 'Bursa', region: 'Marmara' },
        { value: 'canakkale', label: 'Çanakkale', region: 'Marmara' },
        { value: 'cankiri', label: 'Çankırı', region: 'İç Anadolu' },
        { value: 'corum', label: 'Çorum', region: 'Karadeniz' },
        { value: 'denizli', label: 'Denizli', region: 'Ege' },
        { value: 'diyarbakir', label: 'Diyarbakır', region: 'Güneydoğu Anadolu' },
        { value: 'duzce', label: 'Düzce', region: 'Karadeniz' },
        { value: 'edirne', label: 'Edirne', region: 'Marmara' },
        { value: 'elazig', label: 'Elazığ', region: 'Doğu Anadolu' },
        { value: 'erzincan', label: 'Erzincan', region: 'Doğu Anadolu' },
        { value: 'erzurum', label: 'Erzurum', region: 'Doğu Anadolu' },
        { value: 'eskisehir', label: 'Eskişehir', region: 'İç Anadolu' },
        { value: 'gaziantep', label: 'Gaziantep', region: 'Güneydoğu Anadolu' },
        { value: 'giresun', label: 'Giresun', region: 'Karadeniz' },
        { value: 'gumushane', label: 'Gümüşhane', region: 'Karadeniz' },
        { value: 'hakkari', label: 'Hakkari', region: 'Doğu Anadolu' },
        { value: 'hatay', label: 'Hatay', region: 'Akdeniz' },
        { value: 'igdir', label: 'Iğdır', region: 'Doğu Anadolu' },
        { value: 'isparta', label: 'Isparta', region: 'Akdeniz' },
        { value: 'istanbul', label: 'İstanbul', region: 'Marmara' },
        { value: 'izmir', label: 'İzmir', region: 'Ege' },
        { value: 'kahramanmaras', label: 'Kahramanmaraş', region: 'Akdeniz' },
        { value: 'karabuk', label: 'Karabük', region: 'Karadeniz' },
        { value: 'karaman', label: 'Karaman', region: 'İç Anadolu' },
        { value: 'kars', label: 'Kars', region: 'Doğu Anadolu' },
        { value: 'kastamonu', label: 'Kastamonu', region: 'Karadeniz' },
        { value: 'kayseri', label: 'Kayseri', region: 'İç Anadolu' },
        { value: 'kilis', label: 'Kilis', region: 'Güneydoğu Anadolu' },
        { value: 'kirikkale', label: 'Kırıkkale', region: 'İç Anadolu' },
        { value: 'kirklareli', label: 'Kırklareli', region: 'Marmara' },
        { value: 'kirsehir', label: 'Kırşehir', region: 'İç Anadolu' },
        { value: 'kocaeli', label: 'Kocaeli', region: 'Marmara' },
        { value: 'konya', label: 'Konya', region: 'İç Anadolu' }
    ];

    res.json({
        success: true,
        data: cities
    });
});

// Gayrimenkul türleri endpoint'i
app.get('/api/property-types', (req, res) => {
    const propertyTypes = [
        { value: 'daire', label: 'Daire' },
        { value: 'villa', label: 'Villa' },
        { value: 'dubleks', label: 'Dubleks' },
        { value: 'mustakil', label: 'Müstakil Ev' }
    ];

    res.json({
        success: true,
        data: propertyTypes
    });
});

// TAM KAPASİTE Sistem istatistikleri endpoint'i
app.get('/api/stats', async (req, res) => {
    try {
        // ML API sağlık kontrolü ve performans bilgileri
        let mlApiStatus = 'down';
        let mlApiPerformance = null;

        try {
            const healthResponse = await callMLAPI('/api/health');
            mlApiStatus = 'up';

            // TAM KAPASİTE modunda performans bilgilerini de al
            if (FULL_CAPACITY_MODE) {
                try {
                    mlApiPerformance = await callMLAPI('/api/performance');
                } catch (perfError) {
                    console.log('ML API performans bilgileri alınamadı:', perfError.message);
                }
            }
        } catch (error) {
            console.log('ML API erişilemez:', error.message);
        }

        const memUsage = process.memoryUsage();

        const stats = {
            success: true,
            data: {
                // Temel sistem bilgileri
                backend_status: 'up',
                ml_api_status: mlApiStatus,
                uptime: process.uptime(),
                node_version: process.version,
                timestamp: new Date().toISOString(),

                // TAM KAPASİTE bilgileri
                full_capacity_mode: FULL_CAPACITY_MODE,
                max_request_size: MAX_REQUEST_SIZE,
                request_timeout: REQUEST_TIMEOUT,

                // Gelişmiş memory bilgileri
                memory_usage: {
                    rss: `${Math.round(memUsage.rss / 1024 / 1024)} MB`,
                    heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)} MB`,
                    heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`,
                    external: `${Math.round(memUsage.external / 1024 / 1024)} MB`,
                    arrayBuffers: `${Math.round(memUsage.arrayBuffers / 1024 / 1024)} MB`
                },

                // ML API performans bilgileri (varsa)
                ml_api_performance: mlApiPerformance?.performance || null,

                // Sistem kapasitesi
                system_capacity: {
                    status: FULL_CAPACITY_MODE ? 'TAM KAPASİTE AKTİF' : 'Normal Mod',
                    data_processing: FULL_CAPACITY_MODE ? 'Unlimited' : 'Limited',
                    compression_level: FULL_CAPACITY_MODE ? 'Maximum (9)' : 'Standard (6)',
                    parameter_limit: FULL_CAPACITY_MODE ? '100,000' : '1,000'
                }
            }
        };

        res.json(stats);

    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'İstatistikler alınırken hata oluştu',
            message: error.message
        });
    }
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint bulunamadı',
        path: req.originalUrl
    });
});

// Global error handler
app.use((error, req, res, next) => {
    console.error('Global error:', error);
    res.status(500).json({
        success: false,
        error: 'Sunucu hatası',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Bir hata oluştu'
    });
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM alındı, sunucu kapatılıyor...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT alındı, sunucu kapatılıyor...');
    process.exit(0);
});

// TAM KAPASİTE Sunucuyu başlat
app.listen(PORT, () => {
    console.log('\n' + '='.repeat(80));
    console.log('🚀 TAM KAPASİTE GAYRIMENKUL BACKEND SERVER HAZIR!');
    console.log('='.repeat(80));
    console.log(`🌐 Server URL: http://localhost:${PORT}`);
    console.log(`🤖 ML API URL: ${ML_API_URL}`);
    console.log(`🚀 Tam Kapasite Modu: ${FULL_CAPACITY_MODE ? 'AKTİF ✅' : 'DEVRE DIŞI ❌'}`);
    console.log(`📊 Max Request Size: ${MAX_REQUEST_SIZE}`);
    console.log(`⏱️ Request Timeout: ${REQUEST_TIMEOUT}ms`);
    console.log(`💾 Memory Limit: ${FULL_CAPACITY_MODE ? 'Unlimited' : 'Standard'}`);
    console.log(`🗜️ Compression: ${FULL_CAPACITY_MODE ? 'Maximum (Level 9)' : 'Standard (Level 6)'}`);
    console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🕐 Başlatma zamanı: ${new Date().toISOString()}`);
    console.log('='.repeat(80));
    console.log('📋 Mevcut Endpoint\'ler:');
    console.log('   GET  /health                 - Sistem sağlık kontrolü');
    console.log('   POST /api/predict            - Fiyat tahmini');
    console.log('   POST /api/filter-properties  - İlan arama ve filtreleme');
    console.log('   POST /api/investment-analysis - Yatırım analizi');
    console.log('   GET  /api/market-analysis    - Piyasa analizi');
    console.log('   GET  /api/cities             - Şehir listesi');
    console.log('   GET  /api/property-types     - Gayrimenkul türleri');
    console.log('   GET  /api/stats              - Sistem istatistikleri');
    console.log('='.repeat(80));

    if (FULL_CAPACITY_MODE) {
        console.log('🎯 TAM KAPASİTE MODU AKTİF - SİSTEM HAZIR!');
    } else {
        console.log('📊 Normal mod aktif');
    }
    console.log('');
});

module.exports = app;
