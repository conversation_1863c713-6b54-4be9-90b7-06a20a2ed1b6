const axios = require('axios');

async function testAPI() {
    try {
        console.log('🔍 Backend API test başlıyor...');
        
        // Health check
        const healthResponse = await axios.get('http://localhost:3001/health');
        console.log('✅ Health check:', healthResponse.data);
        
        // Filter properties test - İstanbul şehri filtresi
        const filterData = {
            city: 'istanbul',
            min_price: 1000000,
            max_price: 5000000,
            limit: 10
        };
        
        console.log('🔍 Filter test başlıyor...');
        console.log('📋 Gönderilen data:', filterData);
        
        const filterResponse = await axios.post('http://localhost:3001/api/filter-properties', filterData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        
        console.log('✅ Filter response status:', filterResponse.status);
        console.log('✅ Filter response data:', JSON.stringify(filterResponse.data, null, 2));
        
    } catch (error) {
        console.error('❌ Test hatası:', error.message);
        if (error.response) {
            console.error('❌ Response status:', error.response.status);
            console.error('❌ Response data:', error.response.data);
        }
    }
}

testAPI();
