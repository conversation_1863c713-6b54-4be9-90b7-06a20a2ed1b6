const axios = require('axios');

async function testAPI() {
    try {
        console.log('🔍 Backend API test başlıyor...');
        
        // Health check
        const healthResponse = await axios.get('http://localhost:3001/health');
        console.log('✅ Health check:', healthResponse.data);
        
        // Filter properties test - İstanbul şehri filtresi
        const filterData = {
            city: 'istanbul',
            min_price: 1000000,
            max_price: 5000000,
            limit: 10
        };
        
        console.log('🔍 Filter test başlıyor...');
        console.log('📋 Gönderilen data:', filterData);
        
        const filterResponse = await axios.post('http://localhost:3001/api/filter-properties', filterData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        
        console.log('✅ Filter response status:', filterResponse.status);
        console.log('✅ Filter response data:', JSON.stringify(filterResponse.data, null, 2));

        // Piyasa analizi test
        console.log('\n🔍 Piyasa analizi test başlıyor...');
        const marketResponse = await axios.get('http://localhost:3001/api/market-analysis', {
            timeout: 30000
        });

        console.log('✅ Market analysis status:', marketResponse.status);
        console.log('✅ Market analysis data:', JSON.stringify(marketResponse.data, null, 2));

        // Fiyat tahmini test
        console.log('\n🔍 Fiyat tahmini test başlıyor...');
        const priceData = {
            Net_Metrekare: "120",
            Oda_Sayısı: 3,
            Şehir: "istanbul",
            Binanın_Yaşı: "1-5",
            Eşya_Durumu: "Boş",
            Isıtma_Tipi: "Kombi Doğalgaz",
            Bulunduğu_Kat: "2.Kat",
            Binanın_Kat_Sayısı: "5",
            Banyo_Sayısı: 2
        };

        const priceResponse = await axios.post('http://localhost:3001/api/predict-price', priceData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });

        console.log('✅ Price prediction status:', priceResponse.status);
        console.log('✅ Price prediction data:', JSON.stringify(priceResponse.data, null, 2));

    } catch (error) {
        console.error('❌ Test hatası:', error.message);
        if (error.response) {
            console.error('❌ Response status:', error.response.status);
            console.error('❌ Response data:', error.response.data);
        }
    }
}

testAPI();
