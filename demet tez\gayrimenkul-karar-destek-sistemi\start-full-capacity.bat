@echo off
echo.
echo ================================================================================
echo 🚀 TAM KAPASİTE GAYRIMENKUL KARAR DESTEK SİSTEMİ BAŞLATILIYOR
echo ================================================================================
echo.

REM Environment variables dosyasını kopyala
if exist .env.full-capacity (
    copy .env.full-capacity .env
    echo ✅ TAM KAPASİTE environment variables yüklendi
) else (
    echo ⚠️ .env.full-capacity dosyası bulunamadı, varsayılan ayarlar kullanılacak
)

echo.
echo 📋 TAM KAPASİTE MODU AKTİF:
echo    - Unlimited data processing
echo    - Maximum compression (Level 9)
echo    - 100MB request size limit
echo    - 2 minute timeout
echo    - Advanced caching
echo    - Memory optimization
echo    - Parallel processing
echo.

REM Terminal'leri aç
echo 🚀 ML API başlatılıyor (Python Flask)...
start "TAM KAPASİTE ML API" cmd /k "cd ml-models && python api.py"

timeout /t 5 /nobreak >nul

echo 🚀 Backend API başlatılıyor (Node.js)...
start "TAM KAPASİTE Backend" cmd /k "cd backend && npm start"

timeout /t 3 /nobreak >nul

echo 🚀 Frontend başlatılıyor (React)...
start "TAM KAPASİTE Frontend" cmd /k "cd frontend && npm start"

echo.
echo ================================================================================
echo 🎉 TAM KAPASİTE SİSTEM BAŞLATILDI!
echo ================================================================================
echo.
echo 📊 Servisler:
echo    🤖 ML API:      http://localhost:5000
echo    🔧 Backend:     http://localhost:3001  
echo    🌐 Frontend:    http://localhost:3000
echo.
echo 📈 Performans İzleme:
echo    📊 Stats:       http://localhost:3001/api/stats
echo    ⚡ Performance: http://localhost:5000/api/performance
echo.
echo 🎯 TAM KAPASİTE MODU AKTİF - SİSTEM HAZIR!
echo ================================================================================
echo.

pause
