export interface UnsubscriptionError extends Error {
    readonly errors: any[];
}
export interface UnsubscriptionErrorCtor {
    /**
     * @deprecated Internal implementation detail. Do not construct error instances.
     * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269
     */
    new (errors: any[]): UnsubscriptionError;
}
/**
 * An error thrown when one or more errors have occurred during the
 * `unsubscribe` of a {@link Subscription}.
 */
export declare const UnsubscriptionError: UnsubscriptionErrorCtor;
//# sourceMappingURL=UnsubscriptionError.d.ts.map