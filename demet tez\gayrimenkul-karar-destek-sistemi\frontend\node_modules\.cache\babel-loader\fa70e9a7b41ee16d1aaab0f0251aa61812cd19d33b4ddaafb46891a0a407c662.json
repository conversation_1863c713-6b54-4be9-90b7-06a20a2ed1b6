{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demet tez\\\\demet tez\\\\gayrimenkul-karar-destek-sistemi\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Grid, Card, CardContent, Typography, Button, Box, Chip } from '@mui/material';\nimport { TrendingUp as PredictionIcon, Analytics as AnalyticsIcon, Search as SearchIcon, Home as HomeIcon, Assessment as InvestmentIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  var _marketData$general_s, _marketData$general_s2, _marketData$general_s3, _marketData$general_s4, _marketData$general_s5;\n  const navigate = useNavigate();\n  const features = [{\n    title: '<PERSON><PERSON><PERSON>',\n    description: 'Gelişmiş ML algoritmaları ile doğru fiyat tahmini.',\n    icon: /*#__PURE__*/_jsxDEV(PredictionIcon, {\n      sx: {\n        fontSize: 40,\n        color: 'primary.main'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this),\n    action: () => navigate('/prediction'),\n    buttonText: 'Tahmin Yap'\n  }, {\n    title: 'İlan Arama',\n    description: 'Akıllı filtreleme ile ideal gayrimenkulü bulun.',\n    icon: /*#__PURE__*/_jsxDEV(SearchIcon, {\n      sx: {\n        fontSize: 40,\n        color: 'success.main'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this),\n    action: () => navigate('/properties'),\n    buttonText: 'İlan Ara'\n  }, {\n    title: 'Piyasa Analizi',\n    description: 'Şehir bazında detaylı piyasa analizleri.',\n    icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n      sx: {\n        fontSize: 40,\n        color: 'secondary.main'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this),\n    action: () => navigate('/analysis'),\n    buttonText: 'Analiz Et'\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4,\n        background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',\n        color: 'white'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          py: 6\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 8,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              component: \"h1\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              children: \"Gayrimenkul Karar Destek Sistemi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 3,\n                opacity: 0.9\n              },\n              children: \"Veri madencili\\u011Fi ve makine \\xF6\\u011Frenmesi teknikleri ile gayrimenkul de\\u011Ferlendirmesi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 4,\n                opacity: 0.8\n              },\n              children: \"Bu sistem, geli\\u015Fmi\\u015F makine \\xF6\\u011Frenmesi algoritmalar\\u0131 kullanarak gayrimenkul fiyat tahmini, piyasa analizi ve ak\\u0131ll\\u0131 yat\\u0131r\\u0131m \\xF6nerileri sunar. Veri madencili\\u011Fi teknikleri ile en do\\u011Fru sonu\\xE7lar\\u0131 elde edin.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Random Forest\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"XGBoost\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Stacking Ensemble\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Yat\\u0131r\\u0131m Analizi\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Veri Madencili\\u011Fi\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            textAlign: \"center\",\n            children: /*#__PURE__*/_jsxDEV(HomeIcon, {\n              sx: {\n                fontSize: 120,\n                opacity: 0.3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"warning\",\n      sx: {\n        mb: 3\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this), stats && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Sistem Durumu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: stats.backend_status === 'up' ? '✓' : '✗'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Backend API\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: stats.ml_api_status === 'up' ? '✓' : '✗'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"ML Servisi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: [Math.round(stats.uptime / 60), \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\xC7al\\u0131\\u015Fma S\\xFCresi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: stats.node_version\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Node.js\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h2\",\n      gutterBottom: true,\n      textAlign: \"center\",\n      sx: {\n        mb: 4\n      },\n      children: \"Sistem \\xD6zellikleri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      sx: {\n        mb: 4\n      },\n      children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            minHeight: '300px' // Minimum yükseklik belirle\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1,\n              textAlign: 'center',\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'space-between' // İçeriği eşit dağıt\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: feature.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                component: \"h3\",\n                gutterBottom: true,\n                children: feature.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mb: 2\n                },\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), feature.stats && /*#__PURE__*/_jsxDEV(Chip, {\n                label: feature.stats,\n                color: \"primary\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  mb: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              onClick: feature.action,\n              fullWidth: true,\n              size: \"large\",\n              sx: {\n                mt: 2\n              } // Üstten sabit mesafe\n              ,\n              children: feature.buttonText\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), marketData && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\uD83D\\uDCCA Piyasa \\xD6zeti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"G\\xFCncel piyasa verileri ve istatistikler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Toplam \\u0130lan Say\\u0131s\\u0131\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: ((_marketData$general_s = marketData.general_stats) === null || _marketData$general_s === void 0 ? void 0 : (_marketData$general_s2 = _marketData$general_s.total_properties) === null || _marketData$general_s2 === void 0 ? void 0 : _marketData$general_s2.toLocaleString('tr-TR')) || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Ortalama Fiyat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: (_marketData$general_s3 = marketData.general_stats) !== null && _marketData$general_s3 !== void 0 && _marketData$general_s3.avg_price ? `₺${Math.round(marketData.general_stats.avg_price).toLocaleString('tr-TR')}` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Ortalama Metrekare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: (_marketData$general_s4 = marketData.general_stats) !== null && _marketData$general_s4 !== void 0 && _marketData$general_s4.avg_area ? `${Math.round(marketData.general_stats.avg_area)} m²` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Ortalama Oda Say\\u0131s\\u0131\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: (_marketData$general_s5 = marketData.general_stats) !== null && _marketData$general_s5 !== void 0 && _marketData$general_s5.avg_rooms ? marketData.general_stats.avg_rooms.toFixed(1) : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Box", "Chip", "TrendingUp", "PredictionIcon", "Analytics", "AnalyticsIcon", "Search", "SearchIcon", "Home", "HomeIcon", "Assessment", "InvestmentIcon", "useNavigate", "jsxDEV", "_jsxDEV", "HomePage", "_s", "_marketData$general_s", "_marketData$general_s2", "_marketData$general_s3", "_marketData$general_s4", "_marketData$general_s5", "navigate", "features", "title", "description", "icon", "sx", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "action", "buttonText", "children", "mb", "background", "py", "container", "spacing", "alignItems", "item", "xs", "md", "variant", "component", "gutterBottom", "fontWeight", "opacity", "display", "gap", "flexWrap", "label", "borderColor", "textAlign", "error", "<PERSON><PERSON>", "severity", "stats", "backend_status", "ml_api_status", "Math", "round", "uptime", "node_version", "map", "feature", "index", "sm", "lg", "height", "flexDirection", "minHeight", "flexGrow", "justifyContent", "size", "onClick", "fullWidth", "mt", "marketData", "Divider", "general_stats", "total_properties", "toLocaleString", "avg_price", "avg_area", "avg_rooms", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/demet tez/demet tez/gayrimenkul-karar-destek-sistemi/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON>,\n  <PERSON>,\n  Card<PERSON>ontent,\n  Typo<PERSON>,\n  <PERSON>ton,\n  Box,\n  Chip\n} from '@mui/material';\nimport {\n  TrendingUp as PredictionIcon,\n  Analytics as AnalyticsIcon,\n  Search as SearchIcon,\n  Home as HomeIcon,\n  Assessment as InvestmentIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\n\nconst HomePage = () => {\n  const navigate = useNavigate();\n\n\n\n  const features = [\n    {\n      title: '<PERSON><PERSON><PERSON>',\n      description: 'Gelişmiş ML algoritmaları ile doğru fiyat tahmini.',\n      icon: <PredictionIcon sx={{ fontSize: 40, color: 'primary.main' }} />,\n      action: () => navigate('/prediction'),\n      buttonText: '<PERSON><PERSON><PERSON>'\n    },\n    {\n      title: '<PERSON><PERSON>',\n      description: 'Akıllı filtreleme ile ideal gayrimenkulü bulun.',\n      icon: <SearchIcon sx={{ fontSize: 40, color: 'success.main' }} />,\n      action: () => navigate('/properties'),\n      buttonText: '<PERSON><PERSON> Ara'\n    },\n    {\n      title: '<PERSON><PERSON><PERSON>',\n      description: 'Şehir bazında detaylı piyasa analizleri.',\n      icon: <AnalyticsIcon sx={{ fontSize: 40, color: 'secondary.main' }} />,\n      action: () => navigate('/analysis'),\n      buttonText: 'Analiz Et'\n    }\n  ];\n\n  return (\n    <Box>\n      {/* Hero Section */}\n      <Card sx={{ mb: 4, background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)', color: 'white' }}>\n        <CardContent sx={{ py: 6 }}>\n          <Grid container spacing={4} alignItems=\"center\">\n            <Grid item xs={12} md={8}>\n              <Typography variant=\"h3\" component=\"h1\" gutterBottom fontWeight=\"bold\">\n                Gayrimenkul Karar Destek Sistemi\n              </Typography>\n              <Typography variant=\"h6\" sx={{ mb: 3, opacity: 0.9 }}>\n                Veri madenciliği ve makine öğrenmesi teknikleri ile gayrimenkul değerlendirmesi\n              </Typography>\n              <Typography variant=\"body1\" sx={{ mb: 4, opacity: 0.8 }}>\n                Bu sistem, gelişmiş makine öğrenmesi algoritmaları kullanarak gayrimenkul fiyat tahmini,\n                piyasa analizi ve akıllı yatırım önerileri sunar. Veri madenciliği teknikleri ile\n                en doğru sonuçları elde edin.\n              </Typography>\n              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n                <Chip label=\"Random Forest\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n                <Chip label=\"XGBoost\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n                <Chip label=\"Stacking Ensemble\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n                <Chip label=\"Yatırım Analizi\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n                <Chip label=\"Veri Madenciliği\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n              </Box>\n            </Grid>\n            <Grid item xs={12} md={4} textAlign=\"center\">\n              <HomeIcon sx={{ fontSize: 120, opacity: 0.3 }} />\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* System Status */}\n      {error && (\n        <Alert severity=\"warning\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      {stats && (\n        <Card sx={{ mb: 4 }}>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Sistem Durumu\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={6} md={3}>\n                <Box textAlign=\"center\">\n                  <Typography variant=\"h4\" color=\"primary\">\n                    {stats.backend_status === 'up' ? '✓' : '✗'}\n                  </Typography>\n                  <Typography variant=\"body2\">Backend API</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={6} md={3}>\n                <Box textAlign=\"center\">\n                  <Typography variant=\"h4\" color=\"primary\">\n                    {stats.ml_api_status === 'up' ? '✓' : '✗'}\n                  </Typography>\n                  <Typography variant=\"body2\">ML Servisi</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={6} md={3}>\n                <Box textAlign=\"center\">\n                  <Typography variant=\"h4\" color=\"primary\">\n                    {Math.round(stats.uptime / 60)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Çalışma Süresi</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={6} md={3}>\n                <Box textAlign=\"center\">\n                  <Typography variant=\"h4\" color=\"primary\">\n                    {stats.node_version}\n                  </Typography>\n                  <Typography variant=\"body2\">Node.js</Typography>\n                </Box>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Features */}\n      <Typography variant=\"h4\" component=\"h2\" gutterBottom textAlign=\"center\" sx={{ mb: 4 }}>\n        Sistem Özellikleri\n      </Typography>\n\n      <Grid container spacing={4} sx={{ mb: 4 }}>\n        {features.map((feature, index) => (\n          <Grid item xs={12} sm={6} lg={3} key={index}>\n            <Card sx={{\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column',\n              minHeight: '300px' // Minimum yükseklik belirle\n            }}>\n              <CardContent sx={{\n                flexGrow: 1,\n                textAlign: 'center',\n                display: 'flex',\n                flexDirection: 'column',\n                justifyContent: 'space-between' // İçeriği eşit dağıt\n              }}>\n                <Box>\n                  <Box sx={{ mb: 2 }}>\n                    {feature.icon}\n                  </Box>\n                  <Typography variant=\"h5\" component=\"h3\" gutterBottom>\n                    {feature.title}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    {feature.description}\n                  </Typography>\n                  {feature.stats && (\n                    <Chip\n                      label={feature.stats}\n                      color=\"primary\"\n                      variant=\"outlined\"\n                      size=\"small\"\n                      sx={{ mb: 2 }}\n                    />\n                  )}\n                </Box>\n                <Button\n                  variant=\"contained\"\n                  onClick={feature.action}\n                  fullWidth\n                  size=\"large\"\n                  sx={{ mt: 2 }} // Üstten sabit mesafe\n                >\n                  {feature.buttonText}\n                </Button>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Market Overview */}\n      {marketData && (\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              📊 Piyasa Özeti\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              Güncel piyasa verileri ve istatistikler\n            </Typography>\n            <Divider sx={{ mb: 3 }} />\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Toplam İlan Sayısı\n                </Typography>\n                <Typography variant=\"h4\" color=\"primary\">\n                  {marketData.general_stats?.total_properties?.toLocaleString('tr-TR') || 'N/A'}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Ortalama Fiyat\n                </Typography>\n                <Typography variant=\"h4\" color=\"primary\">\n                  {marketData.general_stats?.avg_price ?\n                    `₺${Math.round(marketData.general_stats.avg_price).toLocaleString('tr-TR')}` :\n                    'N/A'\n                  }\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Ortalama Metrekare\n                </Typography>\n                <Typography variant=\"h4\" color=\"primary\">\n                  {marketData.general_stats?.avg_area ?\n                    `${Math.round(marketData.general_stats.avg_area)} m²` :\n                    'N/A'\n                  }\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Ortalama Oda Sayısı\n                </Typography>\n                <Typography variant=\"h4\" color=\"primary\">\n                  {marketData.general_stats?.avg_rooms ?\n                    marketData.general_stats.avg_rooms.toFixed(1) :\n                    'N/A'\n                  }\n                </Typography>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,GAAG,EACHC,IAAI,QACC,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACrB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAI9B,MAAMW,QAAQ,GAAG,CACf;IACEC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,oDAAoD;IACjEC,IAAI,eAAEZ,OAAA,CAACX,cAAc;MAACwB,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAe;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrEC,MAAM,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,aAAa,CAAC;IACrCa,UAAU,EAAE;EACd,CAAC,EACD;IACEX,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,iDAAiD;IAC9DC,IAAI,eAAEZ,OAAA,CAACP,UAAU;MAACoB,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAe;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjEC,MAAM,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,aAAa,CAAC;IACrCa,UAAU,EAAE;EACd,CAAC,EACD;IACEX,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,0CAA0C;IACvDC,IAAI,eAAEZ,OAAA,CAACT,aAAa;MAACsB,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAiB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtEC,MAAM,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,WAAW,CAAC;IACnCa,UAAU,EAAE;EACd,CAAC,CACF;EAED,oBACErB,OAAA,CAACd,GAAG;IAAAoC,QAAA,gBAEFtB,OAAA,CAAClB,IAAI;MAAC+B,EAAE,EAAE;QAAEU,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE,mDAAmD;QAAET,KAAK,EAAE;MAAQ,CAAE;MAAAO,QAAA,eACnGtB,OAAA,CAACjB,WAAW;QAAC8B,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eACzBtB,OAAA,CAACnB,IAAI;UAAC6C,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAN,QAAA,gBAC7CtB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACvBtB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,IAAI;cAACC,YAAY;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAEvE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACnB,EAAE,EAAE;gBAAEU,EAAE,EAAE,CAAC;gBAAEa,OAAO,EAAE;cAAI,CAAE;cAAAd,QAAA,EAAC;YAEtD;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,OAAO;cAACnB,EAAE,EAAE;gBAAEU,EAAE,EAAE,CAAC;gBAAEa,OAAO,EAAE;cAAI,CAAE;cAAAd,QAAA,EAAC;YAIzD;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAACd,GAAG;cAAC2B,EAAE,EAAE;gBAAEwB,OAAO,EAAE,MAAM;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAjB,QAAA,gBACrDtB,OAAA,CAACb,IAAI;gBAACqD,KAAK,EAAC,eAAe;gBAACR,OAAO,EAAC,UAAU;gBAACnB,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAE0B,WAAW,EAAE;gBAAQ;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/FnB,OAAA,CAACb,IAAI;gBAACqD,KAAK,EAAC,SAAS;gBAACR,OAAO,EAAC,UAAU;gBAACnB,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAE0B,WAAW,EAAE;gBAAQ;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzFnB,OAAA,CAACb,IAAI;gBAACqD,KAAK,EAAC,mBAAmB;gBAACR,OAAO,EAAC,UAAU;gBAACnB,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAE0B,WAAW,EAAE;gBAAQ;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnGnB,OAAA,CAACb,IAAI;gBAACqD,KAAK,EAAC,2BAAiB;gBAACR,OAAO,EAAC,UAAU;gBAACnB,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAE0B,WAAW,EAAE;gBAAQ;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjGnB,OAAA,CAACb,IAAI;gBAACqD,KAAK,EAAC,uBAAkB;gBAACR,OAAO,EAAC,UAAU;gBAACnB,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAE0B,WAAW,EAAE;gBAAQ;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACW,SAAS,EAAC,QAAQ;YAAApB,QAAA,eAC1CtB,OAAA,CAACL,QAAQ;cAACkB,EAAE,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEsB,OAAO,EAAE;cAAI;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGNwB,KAAK,iBACJ3C,OAAA,CAAC4C,KAAK;MAACC,QAAQ,EAAC,SAAS;MAAChC,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,EACrCqB;IAAK;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA2B,KAAK,iBACJ9C,OAAA,CAAClB,IAAI;MAAC+B,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,eAClBtB,OAAA,CAACjB,WAAW;QAAAuC,QAAA,gBACVtB,OAAA,CAAChB,UAAU;UAACgD,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAZ,QAAA,EAAC;QAEtC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnB,OAAA,CAACnB,IAAI;UAAC6C,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAL,QAAA,gBACzBtB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACtBtB,OAAA,CAACd,GAAG;cAACwD,SAAS,EAAC,QAAQ;cAAApB,QAAA,gBACrBtB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,IAAI;gBAACjB,KAAK,EAAC,SAAS;gBAAAO,QAAA,EACrCwB,KAAK,CAACC,cAAc,KAAK,IAAI,GAAG,GAAG,GAAG;cAAG;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,OAAO;gBAAAV,QAAA,EAAC;cAAW;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACtBtB,OAAA,CAACd,GAAG;cAACwD,SAAS,EAAC,QAAQ;cAAApB,QAAA,gBACrBtB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,IAAI;gBAACjB,KAAK,EAAC,SAAS;gBAAAO,QAAA,EACrCwB,KAAK,CAACE,aAAa,KAAK,IAAI,GAAG,GAAG,GAAG;cAAG;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,OAAO;gBAAAV,QAAA,EAAC;cAAU;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACtBtB,OAAA,CAACd,GAAG;cAACwD,SAAS,EAAC,QAAQ;cAAApB,QAAA,gBACrBtB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,IAAI;gBAACjB,KAAK,EAAC,SAAS;gBAAAO,QAAA,GACrC2B,IAAI,CAACC,KAAK,CAACJ,KAAK,CAACK,MAAM,GAAG,EAAE,CAAC,EAAC,GACjC;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,OAAO;gBAAAV,QAAA,EAAC;cAAc;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,eACtBtB,OAAA,CAACd,GAAG;cAACwD,SAAS,EAAC,QAAQ;cAAApB,QAAA,gBACrBtB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,IAAI;gBAACjB,KAAK,EAAC,SAAS;gBAAAO,QAAA,EACrCwB,KAAK,CAACM;cAAY;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,OAAO;gBAAAV,QAAA,EAAC;cAAO;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,eAGDnB,OAAA,CAAChB,UAAU;MAACgD,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAACQ,SAAS,EAAC,QAAQ;MAAC7B,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,EAAC;IAEvF;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbnB,OAAA,CAACnB,IAAI;MAAC6C,SAAS;MAACC,OAAO,EAAE,CAAE;MAACd,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,EACvCb,QAAQ,CAAC4C,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BvD,OAAA,CAACnB,IAAI;QAACgD,IAAI;QAACC,EAAE,EAAE,EAAG;QAAC0B,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAnC,QAAA,eAC9BtB,OAAA,CAAClB,IAAI;UAAC+B,EAAE,EAAE;YACR6C,MAAM,EAAE,MAAM;YACdrB,OAAO,EAAE,MAAM;YACfsB,aAAa,EAAE,QAAQ;YACvBC,SAAS,EAAE,OAAO,CAAC;UACrB,CAAE;UAAAtC,QAAA,eACAtB,OAAA,CAACjB,WAAW;YAAC8B,EAAE,EAAE;cACfgD,QAAQ,EAAE,CAAC;cACXnB,SAAS,EAAE,QAAQ;cACnBL,OAAO,EAAE,MAAM;cACfsB,aAAa,EAAE,QAAQ;cACvBG,cAAc,EAAE,eAAe,CAAC;YAClC,CAAE;YAAAxC,QAAA,gBACAtB,OAAA,CAACd,GAAG;cAAAoC,QAAA,gBACFtB,OAAA,CAACd,GAAG;gBAAC2B,EAAE,EAAE;kBAAEU,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,EAChBgC,OAAO,CAAC1C;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNnB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,IAAI;gBAACC,SAAS,EAAC,IAAI;gBAACC,YAAY;gBAAAZ,QAAA,EACjDgC,OAAO,CAAC5C;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACgD,OAAO,EAAC,OAAO;gBAACjB,KAAK,EAAC,gBAAgB;gBAACF,EAAE,EAAE;kBAAEU,EAAE,EAAE;gBAAE,CAAE;gBAAAD,QAAA,EAC9DgC,OAAO,CAAC3C;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EACZmC,OAAO,CAACR,KAAK,iBACZ9C,OAAA,CAACb,IAAI;gBACHqD,KAAK,EAAEc,OAAO,CAACR,KAAM;gBACrB/B,KAAK,EAAC,SAAS;gBACfiB,OAAO,EAAC,UAAU;gBAClB+B,IAAI,EAAC,OAAO;gBACZlD,EAAE,EAAE;kBAAEU,EAAE,EAAE;gBAAE;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNnB,OAAA,CAACf,MAAM;cACL+C,OAAO,EAAC,WAAW;cACnBgC,OAAO,EAAEV,OAAO,CAAClC,MAAO;cACxB6C,SAAS;cACTF,IAAI,EAAC,OAAO;cACZlD,EAAE,EAAE;gBAAEqD,EAAE,EAAE;cAAE,CAAE,CAAC;cAAA;cAAA5C,QAAA,EAEdgC,OAAO,CAACjC;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA5C6BoC,KAAK;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6CrC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGNgD,UAAU,iBACTnE,OAAA,CAAClB,IAAI;MAAAwC,QAAA,eACHtB,OAAA,CAACjB,WAAW;QAAAuC,QAAA,gBACVtB,OAAA,CAAChB,UAAU;UAACgD,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAZ,QAAA,EAAC;QAEtC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;UAACgD,OAAO,EAAC,OAAO;UAACjB,KAAK,EAAC,gBAAgB;UAACF,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,EAAC;QAElE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnB,OAAA,CAACoE,OAAO;UAACvD,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BnB,OAAA,CAACnB,IAAI;UAAC6C,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAL,QAAA,gBACzBtB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACvBtB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,WAAW;cAACjB,KAAK,EAAC,gBAAgB;cAAAO,QAAA,EAAC;YAEvD;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACjB,KAAK,EAAC,SAAS;cAAAO,QAAA,EACrC,EAAAnB,qBAAA,GAAAgE,UAAU,CAACE,aAAa,cAAAlE,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BmE,gBAAgB,cAAAlE,sBAAA,uBAA1CA,sBAAA,CAA4CmE,cAAc,CAAC,OAAO,CAAC,KAAI;YAAK;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACvBtB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,WAAW;cAACjB,KAAK,EAAC,gBAAgB;cAAAO,QAAA,EAAC;YAEvD;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACjB,KAAK,EAAC,SAAS;cAAAO,QAAA,EACrC,CAAAjB,sBAAA,GAAA8D,UAAU,CAACE,aAAa,cAAAhE,sBAAA,eAAxBA,sBAAA,CAA0BmE,SAAS,GAClC,IAAIvB,IAAI,CAACC,KAAK,CAACiB,UAAU,CAACE,aAAa,CAACG,SAAS,CAAC,CAACD,cAAc,CAAC,OAAO,CAAC,EAAE,GAC5E;YAAK;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACvBtB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,WAAW;cAACjB,KAAK,EAAC,gBAAgB;cAAAO,QAAA,EAAC;YAEvD;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACjB,KAAK,EAAC,SAAS;cAAAO,QAAA,EACrC,CAAAhB,sBAAA,GAAA6D,UAAU,CAACE,aAAa,cAAA/D,sBAAA,eAAxBA,sBAAA,CAA0BmE,QAAQ,GACjC,GAAGxB,IAAI,CAACC,KAAK,CAACiB,UAAU,CAACE,aAAa,CAACI,QAAQ,CAAC,KAAK,GACrD;YAAK;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACgD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAT,QAAA,gBACvBtB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,WAAW;cAACjB,KAAK,EAAC,gBAAgB;cAAAO,QAAA,EAAC;YAEvD;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACgD,OAAO,EAAC,IAAI;cAACjB,KAAK,EAAC,SAAS;cAAAO,QAAA,EACrC,CAAAf,sBAAA,GAAA4D,UAAU,CAACE,aAAa,cAAA9D,sBAAA,eAAxBA,sBAAA,CAA0BmE,SAAS,GAClCP,UAAU,CAACE,aAAa,CAACK,SAAS,CAACC,OAAO,CAAC,CAAC,CAAC,GAC7C;YAAK;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjB,EAAA,CApOID,QAAQ;EAAA,QACKH,WAAW;AAAA;AAAA8E,EAAA,GADxB3E,QAAQ;AAsOd,eAAeA,QAAQ;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}