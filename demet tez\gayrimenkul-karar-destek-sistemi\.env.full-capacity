# 🚀 TAM KAPASİTE MODU - Environment Variables
# Gayrimenkul Karar Destek Sistemi - Maximum Performance Configuration

# TAM KAPASİTE MODU
FULL_CAPACITY_MODE=true
NODE_ENV=production

# Backend Configuration
PORT=3001
FRONTEND_URL=http://localhost:3000

# ML API Configuration
ML_API_URL=http://localhost:5000
ML_API_PORT=5000

# Performance Settings
MAX_REQUEST_SIZE=100mb
REQUEST_TIMEOUT=120000
DATA_LIMIT=

# Memory and Processing
MEMORY_LIMIT=unlimited
COMPRESSION_LEVEL=9
PARAMETER_LIMIT=100000

# Cache Settings
CACHE_ENABLED=true
CACHE_SIZE=10000

# Database (if used)
DB_CONNECTION_POOL_SIZE=50
DB_TIMEOUT=60000

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Security (relaxed for performance)
HELMET_CSP=false
CORS_MAX_AGE=86400

# Advanced ML Features
ADVANCED_ML_ENABLED=true
PARALLEL_PROCESSING=true
BATCH_SIZE=10000

# System Monitoring
PERFORMANCE_MONITORING=true
STATS_COLLECTION=true

# Development/Debug
DEBUG_MODE=false
VERBOSE_LOGGING=true

# TAM KAPASİTE Specific
UNLIMITED_DATA_PROCESSING=true
VECTORIZED_OPERATIONS=true
MEMORY_OPTIMIZATION=true
CHUNK_PROCESSING=true
