import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  <PERSON><PERSON>,
  Box,
  Chip
} from '@mui/material';
import {
  TrendingUp as PredictionIcon,
  Analytics as AnalyticsIcon,
  Search as SearchIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const HomePage = () => {
  const navigate = useNavigate();



  const features = [
    {
      title: '<PERSON><PERSON><PERSON>hm<PERSON>',
      description: 'Gelişmiş ML algoritmaları ile doğru fiyat tahmini.',
      icon: <PredictionIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
      action: () => navigate('/prediction'),
      buttonText: '<PERSON><PERSON><PERSON>'
    },
    {
      title: '<PERSON><PERSON>',
      description: 'Akıllı filtreleme ile ideal gayrimenkulü bulun.',
      icon: <SearchIcon sx={{ fontSize: 40, color: 'success.main' }} />,
      action: () => navigate('/properties'),
      buttonText: '<PERSON><PERSON>'
    },
    {
      title: '<PERSON><PERSON><PERSON>',
      description: 'Şehir bazında detaylı piyasa analizleri.',
      icon: <AnalyticsIcon sx={{ fontSize: 40, color: 'secondary.main' }} />,
      action: () => navigate('/analysis'),
      buttonText: 'Analiz Et'
    }
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Card sx={{ mb: 4, background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)', color: 'white' }}>
        <CardContent sx={{ py: 6 }}>
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
                Gayrimenkul Karar Destek Sistemi
              </Typography>
              <Typography variant="h6" sx={{ mb: 3, opacity: 0.9 }}>
                Veri madenciliği ve makine öğrenmesi teknikleri ile gayrimenkul değerlendirmesi
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
                Bu sistem, gelişmiş makine öğrenmesi algoritmaları kullanarak gayrimenkul fiyat tahmini,
                piyasa analizi ve akıllı yatırım önerileri sunar.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Chip label="Random Forest" variant="outlined" sx={{ color: 'white', borderColor: 'white' }} />
                <Chip label="XGBoost" variant="outlined" sx={{ color: 'white', borderColor: 'white' }} />
                <Chip label="Veri Madenciliği" variant="outlined" sx={{ color: 'white', borderColor: 'white' }} />
              </Box>
            </Grid>
            <Grid item xs={12} md={4} textAlign="center">
              <HomeIcon sx={{ fontSize: 120, opacity: 0.3 }} />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Features */}
      <Typography variant="h4" component="h2" gutterBottom textAlign="center" sx={{ mb: 4 }}>
        Sistem Özellikleri
      </Typography>

      <Grid container spacing={4}>
        {features.map((feature, index) => (
          <Grid item xs={12} sm={6} lg={4} key={index}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                <Box sx={{ mb: 2 }}>
                  {feature.icon}
                </Box>
                <Typography variant="h5" component="h3" gutterBottom>
                  {feature.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {feature.description}
                </Typography>
                <Button
                  variant="contained"
                  onClick={feature.action}
                  fullWidth
                  size="large"
                >
                  {feature.buttonText}
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default HomePage;
