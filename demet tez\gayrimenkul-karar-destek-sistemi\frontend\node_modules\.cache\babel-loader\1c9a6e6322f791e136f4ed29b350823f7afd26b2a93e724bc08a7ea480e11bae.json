{"ast": null, "code": "/**\n * API Servis <PERSON>\n * Backend API'si ile iletişim k<PERSON>\n */\n\nimport axios from 'axios';\n\n// API base URL - Backend server portu (3003)\nconst API_BASE_URL = 'http://localhost:3003';\n\n// Axios instance oluştur\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor\napi.interceptors.request.use(config => {\n  var _config$method;\n  console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n  return config;\n}, error => {\n  console.error('API Request Error:', error);\n  return Promise.reject(error);\n});\n\n// Response interceptor\napi.interceptors.response.use(response => {\n  console.log(`API Response: ${response.status} ${response.config.url}`);\n  return response;\n}, error => {\n  var _error$response, _error$response2, _error$response3;\n  console.error('API Response Error:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n\n  // Hata mesajını kullanıcı dostu hale getir\n  if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 404) {\n    error.message = 'İstenen kaynak bulunamadı';\n  } else if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 500) {\n    error.message = 'Sunucu hatası oluştu';\n  } else if (error.code === 'ECONNABORTED') {\n    error.message = 'İstek zaman aşımına uğradı';\n  } else if (!error.response) {\n    error.message = 'Sunucuya bağlanılamıyor';\n  }\n  return Promise.reject(error);\n});\n\n// API fonksiyonları\n\n/**\n * Sistem sağlık kontrolü\n */\nexport const getSystemStats = async () => {\n  try {\n    console.log('🔍 Sistem istatistikleri alınıyor...');\n\n    // Backend stats API'sini çağır\n    const response = await api.get('/api/stats');\n    console.log('✅ Backend stats alındı:', response.data);\n    if (response.data.stats) {\n      return {\n        success: true,\n        stats: {\n          backend_status: 'up',\n          ml_api_status: 'up',\n          uptime: 3600,\n          node_version: '18.x',\n          memory_usage: {\n            heapUsed: 50000000,\n            heapTotal: 100000000\n          },\n          // Backend'den gelen gerçek veriler\n          total_properties: response.data.stats.total_properties,\n          avg_price: response.data.stats.avg_price,\n          avg_area: response.data.stats.avg_area,\n          avg_rooms: response.data.stats.avg_rooms\n        }\n      };\n    }\n  } catch (error) {\n    console.warn('❌ Backend stats API başarısız, fallback kullanılıyor:', error);\n  }\n\n  // Fallback veri\n  return {\n    success: true,\n    stats: {\n      backend_status: 'up',\n      ml_api_status: 'up',\n      uptime: 3600,\n      node_version: '18.x',\n      memory_usage: {\n        heapUsed: 50000000,\n        heapTotal: 100000000\n      }\n    }\n  };\n};\n\n/**\n * Gayrimenkul filtreleme\n */\nexport const filterProperties = async filters => {\n  try {\n    var _response$data$data, _response$data$data2, _response$data$data3, _normalizedData$prope;\n    console.log('🚀 FRONTEND: filterProperties çağrıldı');\n    console.log('🔍 Backend API çağrısı yapılıyor:', filters);\n    console.log('🌐 API Base URL:', API_BASE_URL);\n    console.log('📡 Tam URL:', `${API_BASE_URL}/api/filter-properties`);\n    const response = await api.post('/api/filter-properties', filters);\n    console.log('✅ Backend response alındı:', response.data);\n    console.log('📊 Response status:', response.status);\n\n    // Backend'den gelen veri formatını normalize et\n    const normalizedData = {\n      success: response.data.success || true,\n      properties: response.data.properties || ((_response$data$data = response.data.data) === null || _response$data$data === void 0 ? void 0 : _response$data$data.properties) || [],\n      total_count: response.data.total_count || ((_response$data$data2 = response.data.data) === null || _response$data$data2 === void 0 ? void 0 : _response$data$data2.total_count) || 0,\n      pagination: response.data.pagination || ((_response$data$data3 = response.data.data) === null || _response$data$data3 === void 0 ? void 0 : _response$data$data3.pagination) || {\n        current_page: filters.page || 1,\n        total_pages: 1,\n        page_size: filters.limit || 20,\n        has_next: false,\n        has_prev: false\n      }\n    };\n    console.log('🎯 İlk 3 ilan yatırım uygunluk:', (_normalizedData$prope = normalizedData.properties) === null || _normalizedData$prope === void 0 ? void 0 : _normalizedData$prope.slice(0, 3).map(p => p.Yatırıma_Uygunluk || p.yatirim_uygunluk));\n    return normalizedData;\n  } catch (error) {\n    console.error('❌ Backend API çağrısı başarısız:', error);\n    console.error('❌ Hata detayı:', error.message);\n    console.warn('⚠️ Frontend fallback veri döndürülüyor');\n\n    // Frontend fallback veri - 9 KATEGORİLİ YATIRIM UYGUNLUK\n    console.log('⚠️ FRONTEND FALLBACK: Yeni kategoriler ile örnek veri oluşturuluyor');\n    const sampleProperties = [];\n\n    // 9 KATEGORİLİ YATIRIM UYGUNLUK ÖRNEKLERİ\n    const categories = [{\n      name: 'Çok Uygun',\n      pricePerSqm: 7000\n    }, {\n      name: 'Uygun',\n      pricePerSqm: 10000\n    }, {\n      name: 'Makul Fiyat',\n      pricePerSqm: 13000\n    }, {\n      name: 'Kısmen Uygun',\n      pricePerSqm: 16000\n    }, {\n      name: 'Orta Seviye',\n      pricePerSqm: 20000\n    }, {\n      name: 'Değerlendirilmeli',\n      pricePerSqm: 25000\n    }, {\n      name: 'Uygun Değil',\n      pricePerSqm: 32000\n    }, {\n      name: 'Pahalı',\n      pricePerSqm: 40000\n    }, {\n      name: 'Çok Pahalı',\n      pricePerSqm: 50000\n    }];\n    const cities = ['adana', 'ankara', 'istanbul', 'izmir', 'bursa', 'antalya', 'gaziantep'];\n    for (let i = 0; i < 20; i++) {\n      const category = categories[i % categories.length];\n      const area = 80 + i * 5;\n      const price = category.pricePerSqm * area;\n      const city = cities[i % cities.length];\n      console.log(`📊 Frontend fallback ilan ${i + 1}: ${category.name} (${category.pricePerSqm} TL/m²)`);\n      sampleProperties.push({\n        id: i + 1,\n        Net_Metrekare: area,\n        Oda_Sayısı: 2 + i % 4,\n        Fiyat: price,\n        Şehir: city,\n        Yatırıma_Uygunluk: category.name,\n        Eşya_Durumu: i % 2 === 0 ? 'Boş' : 'Eşyalı',\n        Binanın_Yaşı: ['0 (Yeni)', '1-5', '5-10', '11-15', '16-20'][i % 5],\n        Bulunduğu_Kat: String(i % 10 + 1) + '.Kat',\n        Isıtma_Tipi: ['Kombi Doğalgaz', 'Merkezi Doğalgaz', 'Klimalı'][i % 3],\n        Banyo_Sayısı: 1 + i % 2,\n        Binanın_Kat_Sayısı: 5 + i % 5,\n        m2_basi_fiyat: category.pricePerSqm\n      });\n    }\n    return {\n      success: true,\n      properties: sampleProperties,\n      total_count: 19920,\n      pagination: {\n        current_page: filters.page || 1,\n        total_pages: 996,\n        page_size: filters.limit || 20,\n        has_next: true,\n        has_prev: false\n      }\n    };\n  }\n};\n\n/**\n * Piyasa analizi\n */\nexport const getMarketAnalysis = async () => {\n  try {\n    const response = await api.get('/api/market-analysis');\n    return response.data;\n  } catch (error) {\n    console.warn('Market analizi API çağrısı başarısız, varsayılan veri döndürülüyor:', error);\n\n    // Offline varsayılan veri\n    return {\n      success: true,\n      general_stats: {\n        total_properties: 19920,\n        avg_price: 2500000,\n        median_price: 2200000,\n        min_price: 150000,\n        max_price: 15000000,\n        avg_area: 120,\n        avg_rooms: 3.2\n      },\n      city_analysis: {\n        'istanbul': {\n          mean: 3500000,\n          count: 5000\n        },\n        'ankara': {\n          mean: 2200000,\n          count: 3000\n        },\n        'izmir': {\n          mean: 2800000,\n          count: 2500\n        },\n        'bursa': {\n          mean: 1800000,\n          count: 1500\n        },\n        'adana': {\n          mean: 1600000,\n          count: 1200\n        }\n      }\n    };\n  }\n};\n\n/**\n * Şehir listesi\n */\nexport const getCities = async () => {\n  try {\n    console.log('🔍 API çağrısı yapılıyor: /api/cities');\n    const response = await api.get('/api/cities');\n    console.log('✅ API response alındı:', response);\n    console.log('📊 Response data:', response.data);\n    console.log('📊 Response status:', response.status);\n\n    // Backend'den gelen veri formatını normalize et\n    const cities = response.data.data || response.data.cities || [];\n    console.log('📋 Normalize edilmiş şehirler:', cities.length, 'şehir');\n    return {\n      success: true,\n      data: cities,\n      cities: cities.map(city => typeof city === 'string' ? city : city.value || city.label)\n    };\n  } catch (error) {\n    var _error$response4;\n    console.error('❌ Cities API çağrısı başarısız:', error);\n    console.error('❌ Error message:', error.message);\n    console.error('❌ Error response:', (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data);\n    console.warn('⚠️ Fallback şehir listesi kullanılıyor');\n\n    // Fallback şehir listesi - TÜM ŞEHİRLER\n    const fallbackCities = [{\n      value: 'adana',\n      label: 'Adana'\n    }, {\n      value: 'adiyaman',\n      label: 'Adıyaman'\n    }, {\n      value: 'afyonkarahisar',\n      label: 'Afyonkarahisar'\n    }, {\n      value: 'agri',\n      label: 'Ağrı'\n    }, {\n      value: 'aksaray',\n      label: 'Aksaray'\n    }, {\n      value: 'amasya',\n      label: 'Amasya'\n    }, {\n      value: 'ankara',\n      label: 'Ankara'\n    }, {\n      value: 'antalya',\n      label: 'Antalya'\n    }, {\n      value: 'ardahan',\n      label: 'Ardahan'\n    }, {\n      value: 'artvin',\n      label: 'Artvin'\n    }, {\n      value: 'aydin',\n      label: 'Aydın'\n    }, {\n      value: 'balikesir',\n      label: 'Balıkesir'\n    }, {\n      value: 'bartin',\n      label: 'Bartın'\n    }, {\n      value: 'batman',\n      label: 'Batman'\n    }, {\n      value: 'bayburt',\n      label: 'Bayburt'\n    }, {\n      value: 'bilecik',\n      label: 'Bilecik'\n    }, {\n      value: 'bingol',\n      label: 'Bingöl'\n    }, {\n      value: 'bitlis',\n      label: 'Bitlis'\n    }, {\n      value: 'bolu',\n      label: 'Bolu'\n    }, {\n      value: 'burdur',\n      label: 'Burdur'\n    }, {\n      value: 'bursa',\n      label: 'Bursa'\n    }, {\n      value: 'canakkale',\n      label: 'Çanakkale'\n    }, {\n      value: 'cankiri',\n      label: 'Çankırı'\n    }, {\n      value: 'corum',\n      label: 'Çorum'\n    }, {\n      value: 'denizli',\n      label: 'Denizli'\n    }, {\n      value: 'diyarbakir',\n      label: 'Diyarbakır'\n    }, {\n      value: 'duzce',\n      label: 'Düzce'\n    }, {\n      value: 'edirne',\n      label: 'Edirne'\n    }, {\n      value: 'elazig',\n      label: 'Elazığ'\n    }, {\n      value: 'erzincan',\n      label: 'Erzincan'\n    }, {\n      value: 'erzurum',\n      label: 'Erzurum'\n    }, {\n      value: 'eskisehir',\n      label: 'Eskişehir'\n    }, {\n      value: 'gaziantep',\n      label: 'Gaziantep'\n    }, {\n      value: 'giresun',\n      label: 'Giresun'\n    }, {\n      value: 'gumushane',\n      label: 'Gümüşhane'\n    }, {\n      value: 'hakkari',\n      label: 'Hakkari'\n    }, {\n      value: 'hatay',\n      label: 'Hatay'\n    }, {\n      value: 'igdir',\n      label: 'Iğdır'\n    }, {\n      value: 'isparta',\n      label: 'Isparta'\n    }, {\n      value: 'istanbul',\n      label: 'İstanbul'\n    }, {\n      value: 'izmir',\n      label: 'İzmir'\n    }, {\n      value: 'kahramanmaras',\n      label: 'Kahramanmaraş'\n    }, {\n      value: 'karabuk',\n      label: 'Karabük'\n    }, {\n      value: 'karaman',\n      label: 'Karaman'\n    }, {\n      value: 'kars',\n      label: 'Kars'\n    }, {\n      value: 'kastamonu',\n      label: 'Kastamonu'\n    }, {\n      value: 'kayseri',\n      label: 'Kayseri'\n    }, {\n      value: 'kilis',\n      label: 'Kilis'\n    }, {\n      value: 'kirikkale',\n      label: 'Kırıkkale'\n    }, {\n      value: 'kirklareli',\n      label: 'Kırklareli'\n    }, {\n      value: 'kirsehir',\n      label: 'Kırşehir'\n    }, {\n      value: 'kocaeli',\n      label: 'Kocaeli'\n    }, {\n      value: 'konya',\n      label: 'Konya'\n    }];\n    return {\n      success: true,\n      data: fallbackCities,\n      cities: fallbackCities.map(city => city.value)\n    };\n  }\n};\n\n/**\n * Gayrimenkul türleri\n */\nexport const getPropertyTypes = async () => {\n  try {\n    const response = await api.get('/api/property-types');\n    return response.data;\n  } catch (error) {\n    // Fallback gayrimenkul türleri\n    return {\n      success: true,\n      property_types: ['Daire', 'Villa', 'Müstakil Ev', 'Dubleks', 'Tripleks']\n    };\n  }\n};\n\n/**\n * Fiyat tahmini\n */\nexport const predictPrice = async propertyData => {\n  try {\n    const response = await api.post('/api/predict-price', propertyData);\n    return response.data;\n  } catch (error) {\n    console.warn('Fiyat tahmini API çağrısı başarısız, örnek tahmin döndürülüyor:', error);\n\n    // Offline örnek tahmin\n    const basePrice = 2000000; // 2M TL base\n    const areaMultiplier = propertyData.Net_Metrekare * 15000; // m² başına 15K TL\n    const roomMultiplier = propertyData.Oda_Sayısı * 200000; // Oda başına 200K TL\n\n    const estimatedPrice = basePrice + areaMultiplier + roomMultiplier;\n    return {\n      success: true,\n      predicted_price: estimatedPrice,\n      price_class: {\n        class: estimatedPrice > 3000000 ? 'yüksek' : estimatedPrice < 1500000 ? 'düşük' : 'orta',\n        confidence: 0.85\n      },\n      investment_recommendation: {\n        recommendation: estimatedPrice > 2500000 ? 'Bu fiyat aralığında yatırım için dikkatli değerlendirme önerilir.' : 'Bu fiyat aralığında yatırım fırsatı olabilir.',\n        score: estimatedPrice > 2500000 ? 6 : 8,\n        city_average: 2200000\n      },\n      similar_properties: [{\n        net_metrekare: propertyData.Net_Metrekare - 10,\n        oda_sayisi: propertyData.Oda_Sayısı,\n        fiyat: estimatedPrice - 200000\n      }, {\n        net_metrekare: propertyData.Net_Metrekare + 5,\n        oda_sayisi: propertyData.Oda_Sayısı,\n        fiyat: estimatedPrice + 150000\n      }]\n    };\n  }\n};\n\n/**\n * Sistem sağlık kontrolü (basit)\n */\nexport const healthCheck = async () => {\n  const response = await api.get('/health');\n  return response.data;\n};\n\n// Yardımcı fonksiyonlar\n\n/**\n * Fiyatı Türk Lirası formatında göster\n */\nexport const formatPrice = price => {\n  if (!price) return 'N/A';\n  return `₺${Math.round(price).toLocaleString('tr-TR')}`;\n};\n\n/**\n * Sayıyı Türkçe formatında göster\n */\nexport const formatNumber = number => {\n  if (!number) return 'N/A';\n  return number.toLocaleString('tr-TR');\n};\n\n/**\n * Tarih formatla\n */\nexport const formatDate = dateString => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  return date.toLocaleDateString('tr-TR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\n/**\n * Yatırım skorunu renge çevir\n */\nexport const getInvestmentScoreColor = score => {\n  if (score >= 8) return 'success';\n  if (score >= 6) return 'warning';\n  if (score >= 4) return 'info';\n  return 'error';\n};\n\n/**\n * Fiyat sınıfını Türkçe'ye çevir\n */\nexport const translatePriceClass = priceClass => {\n  if (!priceClass) return 'Belirtilmemiş';\n\n  // Eğer string ise direkt döndür (zaten Türkçe)\n  if (typeof priceClass === 'string') {\n    return priceClass;\n  }\n\n  // Eğer obje ise class veya label alanını kullan\n  if (typeof priceClass === 'object') {\n    if (priceClass.label) return priceClass.label;\n    if (priceClass.class) {\n      const translations = {\n        'low': 'Düşük',\n        'high': 'Yüksek',\n        'medium': 'Orta',\n        'orta': 'Orta',\n        'düşük': 'Düşük',\n        'yüksek': 'Yüksek'\n      };\n      return translations[priceClass.class.toLowerCase()] || priceClass.class;\n    }\n  }\n  return 'Belirtilmemiş';\n};\n\n/**\n * Hata mesajını kullanıcı dostu hale getir\n */\nexport const getErrorMessage = error => {\n  var _error$response5, _error$response5$data, _error$response6, _error$response6$data;\n  if ((_error$response5 = error.response) !== null && _error$response5 !== void 0 && (_error$response5$data = _error$response5.data) !== null && _error$response5$data !== void 0 && _error$response5$data.error) {\n    return error.response.data.error;\n  }\n  if ((_error$response6 = error.response) !== null && _error$response6 !== void 0 && (_error$response6$data = _error$response6.data) !== null && _error$response6$data !== void 0 && _error$response6$data.message) {\n    return error.response.data.message;\n  }\n  return error.message || 'Bilinmeyen bir hata oluştu';\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "_error$response", "_error$response2", "_error$response3", "data", "message", "code", "getSystemStats", "get", "stats", "success", "backend_status", "ml_api_status", "uptime", "node_version", "memory_usage", "heapUsed", "heapTotal", "total_properties", "avg_price", "avg_area", "avg_rooms", "warn", "filterProperties", "filters", "_response$data$data", "_response$data$data2", "_response$data$data3", "_normalizedData$prope", "post", "normalizedData", "properties", "total_count", "pagination", "current_page", "page", "total_pages", "page_size", "limit", "has_next", "has_prev", "slice", "map", "p", "Yatırıma_Uygunluk", "ya<PERSON><PERSON>_u<PERSON>luk", "sampleProperties", "categories", "name", "pricePerSqm", "cities", "i", "category", "length", "area", "price", "city", "push", "id", "Net_Metrekare", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Şehir", "<PERSON><PERSON><PERSON>_<PERSON>", "Binanın_Yaşı", "Bulunduğu_Kat", "String", "Isıtma_Tipi", "Banyo_Sayısı", "Binanın_Kat_Sayısı", "m2_basi_fiyat", "getMarketAnalysis", "general_stats", "median_price", "min_price", "max_price", "city_analysis", "mean", "count", "getCities", "value", "label", "_error$response4", "fallbackCities", "getPropertyTypes", "property_types", "predictPrice", "propertyData", "basePrice", "areaMultiplier", "roomMultiplier", "estimatedPrice", "predicted_price", "price_class", "class", "confidence", "investment_recommendation", "recommendation", "score", "city_average", "similar_properties", "net_metrekare", "oda_sayisi", "fiyat", "healthCheck", "formatPrice", "Math", "round", "toLocaleString", "formatNumber", "number", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getInvestmentScoreColor", "translatePriceClass", "priceClass", "translations", "toLowerCase", "getErrorMessage", "_error$response5", "_error$response5$data", "_error$response6", "_error$response6$data"], "sources": ["C:/Users/<USER>/Desktop/demet tez/demet tez/gayrimenkul-karar-destek-sistemi/frontend/src/services/api.js"], "sourcesContent": ["/**\n * API Servis <PERSON>\n * Backend API'si ile iletişim kurar\n */\n\nimport axios from 'axios';\n\n// API base URL - Backend server portu (3003)\nconst API_BASE_URL = 'http://localhost:3003';\n\n// Axios instance oluştur\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor\napi.interceptors.request.use(\n  (config) => {\n    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    return config;\n  },\n  (error) => {\n    console.error('API Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor\napi.interceptors.response.use(\n  (response) => {\n    console.log(`API Response: ${response.status} ${response.config.url}`);\n    return response;\n  },\n  (error) => {\n    console.error('API Response Error:', error.response?.data || error.message);\n\n    // Hata mesajını kullanıcı dostu hale getir\n    if (error.response?.status === 404) {\n      error.message = 'İstenen kaynak bulunamadı';\n    } else if (error.response?.status === 500) {\n      error.message = 'Sunucu hatası oluştu';\n    } else if (error.code === 'ECONNABORTED') {\n      error.message = 'İstek zaman aşımına uğradı';\n    } else if (!error.response) {\n      error.message = 'Sunucuya bağlanılamıyor';\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// API fonksiyonları\n\n/**\n * Sistem sağlık kontrolü\n */\nexport const getSystemStats = async () => {\n  try {\n    console.log('🔍 Sistem istatistikleri alınıyor...');\n\n    // Backend stats API'sini çağır\n    const response = await api.get('/api/stats');\n    console.log('✅ Backend stats alındı:', response.data);\n\n    if (response.data.stats) {\n      return {\n        success: true,\n        stats: {\n          backend_status: 'up',\n          ml_api_status: 'up',\n          uptime: 3600,\n          node_version: '18.x',\n          memory_usage: {\n            heapUsed: 50000000,\n            heapTotal: 100000000\n          },\n          // Backend'den gelen gerçek veriler\n          total_properties: response.data.stats.total_properties,\n          avg_price: response.data.stats.avg_price,\n          avg_area: response.data.stats.avg_area,\n          avg_rooms: response.data.stats.avg_rooms\n        }\n      };\n    }\n  } catch (error) {\n    console.warn('❌ Backend stats API başarısız, fallback kullanılıyor:', error);\n  }\n\n  // Fallback veri\n  return {\n    success: true,\n    stats: {\n      backend_status: 'up',\n      ml_api_status: 'up',\n      uptime: 3600,\n      node_version: '18.x',\n      memory_usage: {\n        heapUsed: 50000000,\n        heapTotal: 100000000\n      }\n    }\n  };\n};\n\n\n\n/**\n * Gayrimenkul filtreleme\n */\nexport const filterProperties = async (filters) => {\n  try {\n    console.log('🚀 FRONTEND: filterProperties çağrıldı');\n    console.log('🔍 Backend API çağrısı yapılıyor:', filters);\n    console.log('🌐 API Base URL:', API_BASE_URL);\n    console.log('📡 Tam URL:', `${API_BASE_URL}/api/filter-properties`);\n\n    const response = await api.post('/api/filter-properties', filters);\n    console.log('✅ Backend response alındı:', response.data);\n    console.log('📊 Response status:', response.status);\n\n    // Backend'den gelen veri formatını normalize et\n    const normalizedData = {\n      success: response.data.success || true,\n      properties: response.data.properties || response.data.data?.properties || [],\n      total_count: response.data.total_count || response.data.data?.total_count || 0,\n      pagination: response.data.pagination || response.data.data?.pagination || {\n        current_page: filters.page || 1,\n        total_pages: 1,\n        page_size: filters.limit || 20,\n        has_next: false,\n        has_prev: false\n      }\n    };\n\n    console.log('🎯 İlk 3 ilan yatırım uygunluk:', normalizedData.properties?.slice(0,3).map(p => p.Yatırıma_Uygunluk || p.yatirim_uygunluk));\n    return normalizedData;\n\n  } catch (error) {\n    console.error('❌ Backend API çağrısı başarısız:', error);\n    console.error('❌ Hata detayı:', error.message);\n    console.warn('⚠️ Frontend fallback veri döndürülüyor');\n\n    // Frontend fallback veri - 9 KATEGORİLİ YATIRIM UYGUNLUK\n    console.log('⚠️ FRONTEND FALLBACK: Yeni kategoriler ile örnek veri oluşturuluyor');\n    const sampleProperties = [];\n\n    // 9 KATEGORİLİ YATIRIM UYGUNLUK ÖRNEKLERİ\n    const categories = [\n      { name: 'Çok Uygun', pricePerSqm: 7000 },\n      { name: 'Uygun', pricePerSqm: 10000 },\n      { name: 'Makul Fiyat', pricePerSqm: 13000 },\n      { name: 'Kısmen Uygun', pricePerSqm: 16000 },\n      { name: 'Orta Seviye', pricePerSqm: 20000 },\n      { name: 'Değerlendirilmeli', pricePerSqm: 25000 },\n      { name: 'Uygun Değil', pricePerSqm: 32000 },\n      { name: 'Pahalı', pricePerSqm: 40000 },\n      { name: 'Çok Pahalı', pricePerSqm: 50000 }\n    ];\n\n    const cities = ['adana', 'ankara', 'istanbul', 'izmir', 'bursa', 'antalya', 'gaziantep'];\n\n    for (let i = 0; i < 20; i++) {\n      const category = categories[i % categories.length];\n      const area = 80 + (i * 5);\n      const price = category.pricePerSqm * area;\n      const city = cities[i % cities.length];\n\n      console.log(`📊 Frontend fallback ilan ${i+1}: ${category.name} (${category.pricePerSqm} TL/m²)`);\n\n      sampleProperties.push({\n        id: i + 1,\n        Net_Metrekare: area,\n        Oda_Sayısı: 2 + (i % 4),\n        Fiyat: price,\n        Şehir: city,\n        Yatırıma_Uygunluk: category.name,\n        Eşya_Durumu: i % 2 === 0 ? 'Boş' : 'Eşyalı',\n        Binanın_Yaşı: ['0 (Yeni)', '1-5', '5-10', '11-15', '16-20'][i % 5],\n        Bulunduğu_Kat: String((i % 10) + 1) + '.Kat',\n        Isıtma_Tipi: ['Kombi Doğalgaz', 'Merkezi Doğalgaz', 'Klimalı'][i % 3],\n        Banyo_Sayısı: 1 + (i % 2),\n        Binanın_Kat_Sayısı: 5 + (i % 5),\n        m2_basi_fiyat: category.pricePerSqm\n      });\n    }\n\n    return {\n      success: true,\n      properties: sampleProperties,\n      total_count: 19920,\n      pagination: {\n        current_page: filters.page || 1,\n        total_pages: 996,\n        page_size: filters.limit || 20,\n        has_next: true,\n        has_prev: false\n      }\n    };\n  }\n};\n\n/**\n * Piyasa analizi\n */\nexport const getMarketAnalysis = async () => {\n  try {\n    const response = await api.get('/api/market-analysis');\n    return response.data;\n  } catch (error) {\n    console.warn('Market analizi API çağrısı başarısız, varsayılan veri döndürülüyor:', error);\n\n    // Offline varsayılan veri\n    return {\n      success: true,\n      general_stats: {\n        total_properties: 19920,\n        avg_price: 2500000,\n        median_price: 2200000,\n        min_price: 150000,\n        max_price: 15000000,\n        avg_area: 120,\n        avg_rooms: 3.2\n      },\n      city_analysis: {\n        'istanbul': { mean: 3500000, count: 5000 },\n        'ankara': { mean: 2200000, count: 3000 },\n        'izmir': { mean: 2800000, count: 2500 },\n        'bursa': { mean: 1800000, count: 1500 },\n        'adana': { mean: 1600000, count: 1200 }\n      }\n    };\n  }\n};\n\n/**\n * Şehir listesi\n */\nexport const getCities = async () => {\n  try {\n    console.log('🔍 API çağrısı yapılıyor: /api/cities');\n    const response = await api.get('/api/cities');\n    console.log('✅ API response alındı:', response);\n    console.log('📊 Response data:', response.data);\n    console.log('📊 Response status:', response.status);\n\n    // Backend'den gelen veri formatını normalize et\n    const cities = response.data.data || response.data.cities || [];\n    console.log('📋 Normalize edilmiş şehirler:', cities.length, 'şehir');\n\n    return {\n      success: true,\n      data: cities,\n      cities: cities.map(city => typeof city === 'string' ? city : city.value || city.label)\n    };\n  } catch (error) {\n    console.error('❌ Cities API çağrısı başarısız:', error);\n    console.error('❌ Error message:', error.message);\n    console.error('❌ Error response:', error.response?.data);\n    console.warn('⚠️ Fallback şehir listesi kullanılıyor');\n\n    // Fallback şehir listesi - TÜM ŞEHİRLER\n    const fallbackCities = [\n      { value: 'adana', label: 'Adana' },\n      { value: 'adiyaman', label: 'Adıyaman' },\n      { value: 'afyonkarahisar', label: 'Afyonkarahisar' },\n      { value: 'agri', label: 'Ağrı' },\n      { value: 'aksaray', label: 'Aksaray' },\n      { value: 'amasya', label: 'Amasya' },\n      { value: 'ankara', label: 'Ankara' },\n      { value: 'antalya', label: 'Antalya' },\n      { value: 'ardahan', label: 'Ardahan' },\n      { value: 'artvin', label: 'Artvin' },\n      { value: 'aydin', label: 'Aydın' },\n      { value: 'balikesir', label: 'Balıkesir' },\n      { value: 'bartin', label: 'Bartın' },\n      { value: 'batman', label: 'Batman' },\n      { value: 'bayburt', label: 'Bayburt' },\n      { value: 'bilecik', label: 'Bilecik' },\n      { value: 'bingol', label: 'Bingöl' },\n      { value: 'bitlis', label: 'Bitlis' },\n      { value: 'bolu', label: 'Bolu' },\n      { value: 'burdur', label: 'Burdur' },\n      { value: 'bursa', label: 'Bursa' },\n      { value: 'canakkale', label: 'Çanakkale' },\n      { value: 'cankiri', label: 'Çankırı' },\n      { value: 'corum', label: 'Çorum' },\n      { value: 'denizli', label: 'Denizli' },\n      { value: 'diyarbakir', label: 'Diyarbakır' },\n      { value: 'duzce', label: 'Düzce' },\n      { value: 'edirne', label: 'Edirne' },\n      { value: 'elazig', label: 'Elazığ' },\n      { value: 'erzincan', label: 'Erzincan' },\n      { value: 'erzurum', label: 'Erzurum' },\n      { value: 'eskisehir', label: 'Eskişehir' },\n      { value: 'gaziantep', label: 'Gaziantep' },\n      { value: 'giresun', label: 'Giresun' },\n      { value: 'gumushane', label: 'Gümüşhane' },\n      { value: 'hakkari', label: 'Hakkari' },\n      { value: 'hatay', label: 'Hatay' },\n      { value: 'igdir', label: 'Iğdır' },\n      { value: 'isparta', label: 'Isparta' },\n      { value: 'istanbul', label: 'İstanbul' },\n      { value: 'izmir', label: 'İzmir' },\n      { value: 'kahramanmaras', label: 'Kahramanmaraş' },\n      { value: 'karabuk', label: 'Karabük' },\n      { value: 'karaman', label: 'Karaman' },\n      { value: 'kars', label: 'Kars' },\n      { value: 'kastamonu', label: 'Kastamonu' },\n      { value: 'kayseri', label: 'Kayseri' },\n      { value: 'kilis', label: 'Kilis' },\n      { value: 'kirikkale', label: 'Kırıkkale' },\n      { value: 'kirklareli', label: 'Kırklareli' },\n      { value: 'kirsehir', label: 'Kırşehir' },\n      { value: 'kocaeli', label: 'Kocaeli' },\n      { value: 'konya', label: 'Konya' }\n    ];\n\n    return {\n      success: true,\n      data: fallbackCities,\n      cities: fallbackCities.map(city => city.value)\n    };\n  }\n};\n\n/**\n * Gayrimenkul türleri\n */\nexport const getPropertyTypes = async () => {\n  try {\n    const response = await api.get('/api/property-types');\n    return response.data;\n  } catch (error) {\n    // Fallback gayrimenkul türleri\n    return {\n      success: true,\n      property_types: ['Daire', 'Villa', 'Müstakil Ev', 'Dubleks', 'Tripleks']\n    };\n  }\n};\n\n/**\n * Fiyat tahmini\n */\nexport const predictPrice = async (propertyData) => {\n  try {\n    const response = await api.post('/api/predict-price', propertyData);\n    return response.data;\n  } catch (error) {\n    console.warn('Fiyat tahmini API çağrısı başarısız, örnek tahmin döndürülüyor:', error);\n\n    // Offline örnek tahmin\n    const basePrice = 2000000; // 2M TL base\n    const areaMultiplier = propertyData.Net_Metrekare * 15000; // m² başına 15K TL\n    const roomMultiplier = propertyData.Oda_Sayısı * 200000; // Oda başına 200K TL\n\n    const estimatedPrice = basePrice + areaMultiplier + roomMultiplier;\n\n    return {\n      success: true,\n      predicted_price: estimatedPrice,\n      price_class: {\n        class: estimatedPrice > 3000000 ? 'yüksek' : estimatedPrice < 1500000 ? 'düşük' : 'orta',\n        confidence: 0.85\n      },\n      investment_recommendation: {\n        recommendation: estimatedPrice > 2500000 ?\n          'Bu fiyat aralığında yatırım için dikkatli değerlendirme önerilir.' :\n          'Bu fiyat aralığında yatırım fırsatı olabilir.',\n        score: estimatedPrice > 2500000 ? 6 : 8,\n        city_average: 2200000\n      },\n      similar_properties: [\n        {\n          net_metrekare: propertyData.Net_Metrekare - 10,\n          oda_sayisi: propertyData.Oda_Sayısı,\n          fiyat: estimatedPrice - 200000\n        },\n        {\n          net_metrekare: propertyData.Net_Metrekare + 5,\n          oda_sayisi: propertyData.Oda_Sayısı,\n          fiyat: estimatedPrice + 150000\n        }\n      ]\n    };\n  }\n};\n\n/**\n * Sistem sağlık kontrolü (basit)\n */\nexport const healthCheck = async () => {\n  const response = await api.get('/health');\n  return response.data;\n};\n\n\n\n// Yardımcı fonksiyonlar\n\n/**\n * Fiyatı Türk Lirası formatında göster\n */\nexport const formatPrice = (price) => {\n  if (!price) return 'N/A';\n  return `₺${Math.round(price).toLocaleString('tr-TR')}`;\n};\n\n/**\n * Sayıyı Türkçe formatında göster\n */\nexport const formatNumber = (number) => {\n  if (!number) return 'N/A';\n  return number.toLocaleString('tr-TR');\n};\n\n/**\n * Tarih formatla\n */\nexport const formatDate = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  return date.toLocaleDateString('tr-TR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\n/**\n * Yatırım skorunu renge çevir\n */\nexport const getInvestmentScoreColor = (score) => {\n  if (score >= 8) return 'success';\n  if (score >= 6) return 'warning';\n  if (score >= 4) return 'info';\n  return 'error';\n};\n\n/**\n * Fiyat sınıfını Türkçe'ye çevir\n */\nexport const translatePriceClass = (priceClass) => {\n  if (!priceClass) return 'Belirtilmemiş';\n\n  // Eğer string ise direkt döndür (zaten Türkçe)\n  if (typeof priceClass === 'string') {\n    return priceClass;\n  }\n\n  // Eğer obje ise class veya label alanını kullan\n  if (typeof priceClass === 'object') {\n    if (priceClass.label) return priceClass.label;\n    if (priceClass.class) {\n      const translations = {\n        'low': 'Düşük',\n        'high': 'Yüksek',\n        'medium': 'Orta',\n        'orta': 'Orta',\n        'düşük': 'Düşük',\n        'yüksek': 'Yüksek'\n      };\n      return translations[priceClass.class.toLowerCase()] || priceClass.class;\n    }\n  }\n\n  return 'Belirtilmemiş';\n};\n\n/**\n * Hata mesajını kullanıcı dostu hale getir\n */\nexport const getErrorMessage = (error) => {\n  if (error.response?.data?.error) {\n    return error.response.data.error;\n  }\n  if (error.response?.data?.message) {\n    return error.response.data.message;\n  }\n  return error.message || 'Bilinmeyen bir hata oluştu';\n};\n\nexport default api;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,YAAY,GAAG,uBAAuB;;AAE5C;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACVC,OAAO,CAACC,GAAG,CAAC,iBAAAF,cAAA,GAAgBD,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIL,MAAM,CAACM,GAAG,EAAE,CAAC;EACzE,OAAON,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACTL,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;EAC1C,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACK,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,IAAK;EACZR,OAAO,CAACC,GAAG,CAAC,iBAAiBO,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACV,MAAM,CAACM,GAAG,EAAE,CAAC;EACtE,OAAOI,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAK,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;EACTZ,OAAO,CAACK,KAAK,CAAC,qBAAqB,EAAE,EAAAK,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBG,IAAI,KAAIR,KAAK,CAACS,OAAO,CAAC;;EAE3E;EACA,IAAI,EAAAH,gBAAA,GAAAN,KAAK,CAACG,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBF,MAAM,MAAK,GAAG,EAAE;IAClCJ,KAAK,CAACS,OAAO,GAAG,2BAA2B;EAC7C,CAAC,MAAM,IAAI,EAAAF,gBAAA,GAAAP,KAAK,CAACG,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBH,MAAM,MAAK,GAAG,EAAE;IACzCJ,KAAK,CAACS,OAAO,GAAG,sBAAsB;EACxC,CAAC,MAAM,IAAIT,KAAK,CAACU,IAAI,KAAK,cAAc,EAAE;IACxCV,KAAK,CAACS,OAAO,GAAG,4BAA4B;EAC9C,CAAC,MAAM,IAAI,CAACT,KAAK,CAACG,QAAQ,EAAE;IAC1BH,KAAK,CAACS,OAAO,GAAG,yBAAyB;EAC3C;EAEA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;;AAEA;AACA;AACA;AACA,OAAO,MAAMW,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACFhB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;IAEnD;IACA,MAAMO,QAAQ,GAAG,MAAMlB,GAAG,CAAC2B,GAAG,CAAC,YAAY,CAAC;IAC5CjB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,QAAQ,CAACK,IAAI,CAAC;IAErD,IAAIL,QAAQ,CAACK,IAAI,CAACK,KAAK,EAAE;MACvB,OAAO;QACLC,OAAO,EAAE,IAAI;QACbD,KAAK,EAAE;UACLE,cAAc,EAAE,IAAI;UACpBC,aAAa,EAAE,IAAI;UACnBC,MAAM,EAAE,IAAI;UACZC,YAAY,EAAE,MAAM;UACpBC,YAAY,EAAE;YACZC,QAAQ,EAAE,QAAQ;YAClBC,SAAS,EAAE;UACb,CAAC;UACD;UACAC,gBAAgB,EAAEnB,QAAQ,CAACK,IAAI,CAACK,KAAK,CAACS,gBAAgB;UACtDC,SAAS,EAAEpB,QAAQ,CAACK,IAAI,CAACK,KAAK,CAACU,SAAS;UACxCC,QAAQ,EAAErB,QAAQ,CAACK,IAAI,CAACK,KAAK,CAACW,QAAQ;UACtCC,SAAS,EAAEtB,QAAQ,CAACK,IAAI,CAACK,KAAK,CAACY;QACjC;MACF,CAAC;IACH;EACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;IACdL,OAAO,CAAC+B,IAAI,CAAC,uDAAuD,EAAE1B,KAAK,CAAC;EAC9E;;EAEA;EACA,OAAO;IACLc,OAAO,EAAE,IAAI;IACbD,KAAK,EAAE;MACLE,cAAc,EAAE,IAAI;MACpBC,aAAa,EAAE,IAAI;MACnBC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,MAAM;MACpBC,YAAY,EAAE;QACZC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE;MACb;IACF;EACF,CAAC;AACH,CAAC;;AAID;AACA;AACA;AACA,OAAO,MAAMM,gBAAgB,GAAG,MAAOC,OAAO,IAAK;EACjD,IAAI;IAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA;IACFrC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrDD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEgC,OAAO,CAAC;IACzDjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEZ,YAAY,CAAC;IAC7CW,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,GAAGZ,YAAY,wBAAwB,CAAC;IAEnE,MAAMmB,QAAQ,GAAG,MAAMlB,GAAG,CAACgD,IAAI,CAAC,wBAAwB,EAAEL,OAAO,CAAC;IAClEjC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,QAAQ,CAACK,IAAI,CAAC;IACxDb,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,QAAQ,CAACC,MAAM,CAAC;;IAEnD;IACA,MAAM8B,cAAc,GAAG;MACrBpB,OAAO,EAAEX,QAAQ,CAACK,IAAI,CAACM,OAAO,IAAI,IAAI;MACtCqB,UAAU,EAAEhC,QAAQ,CAACK,IAAI,CAAC2B,UAAU,MAAAN,mBAAA,GAAI1B,QAAQ,CAACK,IAAI,CAACA,IAAI,cAAAqB,mBAAA,uBAAlBA,mBAAA,CAAoBM,UAAU,KAAI,EAAE;MAC5EC,WAAW,EAAEjC,QAAQ,CAACK,IAAI,CAAC4B,WAAW,MAAAN,oBAAA,GAAI3B,QAAQ,CAACK,IAAI,CAACA,IAAI,cAAAsB,oBAAA,uBAAlBA,oBAAA,CAAoBM,WAAW,KAAI,CAAC;MAC9EC,UAAU,EAAElC,QAAQ,CAACK,IAAI,CAAC6B,UAAU,MAAAN,oBAAA,GAAI5B,QAAQ,CAACK,IAAI,CAACA,IAAI,cAAAuB,oBAAA,uBAAlBA,oBAAA,CAAoBM,UAAU,KAAI;QACxEC,YAAY,EAAEV,OAAO,CAACW,IAAI,IAAI,CAAC;QAC/BC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAEb,OAAO,CAACc,KAAK,IAAI,EAAE;QAC9BC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE;MACZ;IACF,CAAC;IAEDjD,OAAO,CAACC,GAAG,CAAC,iCAAiC,GAAAoC,qBAAA,GAAEE,cAAc,CAACC,UAAU,cAAAH,qBAAA,uBAAzBA,qBAAA,CAA2Ba,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,IAAID,CAAC,CAACE,gBAAgB,CAAC,CAAC;IACzI,OAAOf,cAAc;EAEvB,CAAC,CAAC,OAAOlC,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxDL,OAAO,CAACK,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACS,OAAO,CAAC;IAC9Cd,OAAO,CAAC+B,IAAI,CAAC,wCAAwC,CAAC;;IAEtD;IACA/B,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;IAClF,MAAMsD,gBAAgB,GAAG,EAAE;;IAE3B;IACA,MAAMC,UAAU,GAAG,CACjB;MAAEC,IAAI,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAK,CAAC,EACxC;MAAED,IAAI,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAM,CAAC,EACrC;MAAED,IAAI,EAAE,aAAa;MAAEC,WAAW,EAAE;IAAM,CAAC,EAC3C;MAAED,IAAI,EAAE,cAAc;MAAEC,WAAW,EAAE;IAAM,CAAC,EAC5C;MAAED,IAAI,EAAE,aAAa;MAAEC,WAAW,EAAE;IAAM,CAAC,EAC3C;MAAED,IAAI,EAAE,mBAAmB;MAAEC,WAAW,EAAE;IAAM,CAAC,EACjD;MAAED,IAAI,EAAE,aAAa;MAAEC,WAAW,EAAE;IAAM,CAAC,EAC3C;MAAED,IAAI,EAAE,QAAQ;MAAEC,WAAW,EAAE;IAAM,CAAC,EACtC;MAAED,IAAI,EAAE,YAAY;MAAEC,WAAW,EAAE;IAAM,CAAC,CAC3C;IAED,MAAMC,MAAM,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC;IAExF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,QAAQ,GAAGL,UAAU,CAACI,CAAC,GAAGJ,UAAU,CAACM,MAAM,CAAC;MAClD,MAAMC,IAAI,GAAG,EAAE,GAAIH,CAAC,GAAG,CAAE;MACzB,MAAMI,KAAK,GAAGH,QAAQ,CAACH,WAAW,GAAGK,IAAI;MACzC,MAAME,IAAI,GAAGN,MAAM,CAACC,CAAC,GAAGD,MAAM,CAACG,MAAM,CAAC;MAEtC9D,OAAO,CAACC,GAAG,CAAC,6BAA6B2D,CAAC,GAAC,CAAC,KAAKC,QAAQ,CAACJ,IAAI,KAAKI,QAAQ,CAACH,WAAW,SAAS,CAAC;MAEjGH,gBAAgB,CAACW,IAAI,CAAC;QACpBC,EAAE,EAAEP,CAAC,GAAG,CAAC;QACTQ,aAAa,EAAEL,IAAI;QACnBM,UAAU,EAAE,CAAC,GAAIT,CAAC,GAAG,CAAE;QACvBU,KAAK,EAAEN,KAAK;QACZO,KAAK,EAAEN,IAAI;QACXZ,iBAAiB,EAAEQ,QAAQ,CAACJ,IAAI;QAChCe,WAAW,EAAEZ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ;QAC3Ca,YAAY,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAACb,CAAC,GAAG,CAAC,CAAC;QAClEc,aAAa,EAAEC,MAAM,CAAEf,CAAC,GAAG,EAAE,GAAI,CAAC,CAAC,GAAG,MAAM;QAC5CgB,WAAW,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,SAAS,CAAC,CAAChB,CAAC,GAAG,CAAC,CAAC;QACrEiB,YAAY,EAAE,CAAC,GAAIjB,CAAC,GAAG,CAAE;QACzBkB,kBAAkB,EAAE,CAAC,GAAIlB,CAAC,GAAG,CAAE;QAC/BmB,aAAa,EAAElB,QAAQ,CAACH;MAC1B,CAAC,CAAC;IACJ;IAEA,OAAO;MACLvC,OAAO,EAAE,IAAI;MACbqB,UAAU,EAAEe,gBAAgB;MAC5Bd,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE;QACVC,YAAY,EAAEV,OAAO,CAACW,IAAI,IAAI,CAAC;QAC/BC,WAAW,EAAE,GAAG;QAChBC,SAAS,EAAEb,OAAO,CAACc,KAAK,IAAI,EAAE;QAC9BC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACZ;IACF,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM+B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;EAC3C,IAAI;IACF,MAAMxE,QAAQ,GAAG,MAAMlB,GAAG,CAAC2B,GAAG,CAAC,sBAAsB,CAAC;IACtD,OAAOT,QAAQ,CAACK,IAAI;EACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;IACdL,OAAO,CAAC+B,IAAI,CAAC,qEAAqE,EAAE1B,KAAK,CAAC;;IAE1F;IACA,OAAO;MACLc,OAAO,EAAE,IAAI;MACb8D,aAAa,EAAE;QACbtD,gBAAgB,EAAE,KAAK;QACvBC,SAAS,EAAE,OAAO;QAClBsD,YAAY,EAAE,OAAO;QACrBC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE,QAAQ;QACnBvD,QAAQ,EAAE,GAAG;QACbC,SAAS,EAAE;MACb,CAAC;MACDuD,aAAa,EAAE;QACb,UAAU,EAAE;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAK,CAAC;QAC1C,QAAQ,EAAE;UAAED,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAK,CAAC;QACxC,OAAO,EAAE;UAAED,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAK,CAAC;QACvC,OAAO,EAAE;UAAED,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAK,CAAC;QACvC,OAAO,EAAE;UAAED,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAK;MACxC;IACF,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;EACnC,IAAI;IACFxF,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD,MAAMO,QAAQ,GAAG,MAAMlB,GAAG,CAAC2B,GAAG,CAAC,aAAa,CAAC;IAC7CjB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,QAAQ,CAAC;IAC/CR,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEO,QAAQ,CAACK,IAAI,CAAC;IAC/Cb,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,QAAQ,CAACC,MAAM,CAAC;;IAEnD;IACA,MAAMkD,MAAM,GAAGnD,QAAQ,CAACK,IAAI,CAACA,IAAI,IAAIL,QAAQ,CAACK,IAAI,CAAC8C,MAAM,IAAI,EAAE;IAC/D3D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0D,MAAM,CAACG,MAAM,EAAE,OAAO,CAAC;IAErE,OAAO;MACL3C,OAAO,EAAE,IAAI;MACbN,IAAI,EAAE8C,MAAM;MACZA,MAAM,EAAEA,MAAM,CAACR,GAAG,CAACc,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACwB,KAAK,IAAIxB,IAAI,CAACyB,KAAK;IACvF,CAAC;EACH,CAAC,CAAC,OAAOrF,KAAK,EAAE;IAAA,IAAAsF,gBAAA;IACd3F,OAAO,CAACK,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvDL,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACS,OAAO,CAAC;IAChDd,OAAO,CAACK,KAAK,CAAC,mBAAmB,GAAAsF,gBAAA,GAAEtF,KAAK,CAACG,QAAQ,cAAAmF,gBAAA,uBAAdA,gBAAA,CAAgB9E,IAAI,CAAC;IACxDb,OAAO,CAAC+B,IAAI,CAAC,wCAAwC,CAAC;;IAEtD;IACA,MAAM6D,cAAc,GAAG,CACrB;MAAEH,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAC,EACxC;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAiB,CAAC,EACpD;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAC,EAChC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAY,CAAC,EAC1C;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAC,EAChC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAY,CAAC,EAC1C;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAa,CAAC,EAC5C;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAC,EACxC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAY,CAAC,EAC1C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAY,CAAC,EAC1C;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAY,CAAC,EAC1C;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAC,EACxC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAC,EAClD;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAC,EAChC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAY,CAAC,EAC1C;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAClC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAY,CAAC,EAC1C;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAa,CAAC,EAC5C;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAC,EACxC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU,CAAC,EACtC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAC,CACnC;IAED,OAAO;MACLvE,OAAO,EAAE,IAAI;MACbN,IAAI,EAAE+E,cAAc;MACpBjC,MAAM,EAAEiC,cAAc,CAACzC,GAAG,CAACc,IAAI,IAAIA,IAAI,CAACwB,KAAK;IAC/C,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMI,gBAAgB,GAAG,MAAAA,CAAA,KAAY;EAC1C,IAAI;IACF,MAAMrF,QAAQ,GAAG,MAAMlB,GAAG,CAAC2B,GAAG,CAAC,qBAAqB,CAAC;IACrD,OAAOT,QAAQ,CAACK,IAAI;EACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;IACd;IACA,OAAO;MACLc,OAAO,EAAE,IAAI;MACb2E,cAAc,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU;IACzE,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAG,MAAOC,YAAY,IAAK;EAClD,IAAI;IACF,MAAMxF,QAAQ,GAAG,MAAMlB,GAAG,CAACgD,IAAI,CAAC,oBAAoB,EAAE0D,YAAY,CAAC;IACnE,OAAOxF,QAAQ,CAACK,IAAI;EACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;IACdL,OAAO,CAAC+B,IAAI,CAAC,iEAAiE,EAAE1B,KAAK,CAAC;;IAEtF;IACA,MAAM4F,SAAS,GAAG,OAAO,CAAC,CAAC;IAC3B,MAAMC,cAAc,GAAGF,YAAY,CAAC5B,aAAa,GAAG,KAAK,CAAC,CAAC;IAC3D,MAAM+B,cAAc,GAAGH,YAAY,CAAC3B,UAAU,GAAG,MAAM,CAAC,CAAC;;IAEzD,MAAM+B,cAAc,GAAGH,SAAS,GAAGC,cAAc,GAAGC,cAAc;IAElE,OAAO;MACLhF,OAAO,EAAE,IAAI;MACbkF,eAAe,EAAED,cAAc;MAC/BE,WAAW,EAAE;QACXC,KAAK,EAAEH,cAAc,GAAG,OAAO,GAAG,QAAQ,GAAGA,cAAc,GAAG,OAAO,GAAG,OAAO,GAAG,MAAM;QACxFI,UAAU,EAAE;MACd,CAAC;MACDC,yBAAyB,EAAE;QACzBC,cAAc,EAAEN,cAAc,GAAG,OAAO,GACtC,mEAAmE,GACnE,+CAA+C;QACjDO,KAAK,EAAEP,cAAc,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC;QACvCQ,YAAY,EAAE;MAChB,CAAC;MACDC,kBAAkB,EAAE,CAClB;QACEC,aAAa,EAAEd,YAAY,CAAC5B,aAAa,GAAG,EAAE;QAC9C2C,UAAU,EAAEf,YAAY,CAAC3B,UAAU;QACnC2C,KAAK,EAAEZ,cAAc,GAAG;MAC1B,CAAC,EACD;QACEU,aAAa,EAAEd,YAAY,CAAC5B,aAAa,GAAG,CAAC;QAC7C2C,UAAU,EAAEf,YAAY,CAAC3B,UAAU;QACnC2C,KAAK,EAAEZ,cAAc,GAAG;MAC1B,CAAC;IAEL,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMa,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,MAAMzG,QAAQ,GAAG,MAAMlB,GAAG,CAAC2B,GAAG,CAAC,SAAS,CAAC;EACzC,OAAOT,QAAQ,CAACK,IAAI;AACtB,CAAC;;AAID;;AAEA;AACA;AACA;AACA,OAAO,MAAMqG,WAAW,GAAIlD,KAAK,IAAK;EACpC,IAAI,CAACA,KAAK,EAAE,OAAO,KAAK;EACxB,OAAO,IAAImD,IAAI,CAACC,KAAK,CAACpD,KAAK,CAAC,CAACqD,cAAc,CAAC,OAAO,CAAC,EAAE;AACxD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAIC,MAAM,IAAK;EACtC,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK;EACzB,OAAOA,MAAM,CAACF,cAAc,CAAC,OAAO,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMG,UAAU,GAAIC,UAAU,IAAK;EACxC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;EAC7B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;EACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;IACtCC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,SAAS;IACdC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAIvB,KAAK,IAAK;EAChD,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,SAAS;EAChC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,SAAS;EAChC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,MAAM;EAC7B,OAAO,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMwB,mBAAmB,GAAIC,UAAU,IAAK;EACjD,IAAI,CAACA,UAAU,EAAE,OAAO,eAAe;;EAEvC;EACA,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IAClC,OAAOA,UAAU;EACnB;;EAEA;EACA,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IAClC,IAAIA,UAAU,CAAC1C,KAAK,EAAE,OAAO0C,UAAU,CAAC1C,KAAK;IAC7C,IAAI0C,UAAU,CAAC7B,KAAK,EAAE;MACpB,MAAM8B,YAAY,GAAG;QACnB,KAAK,EAAE,OAAO;QACd,MAAM,EAAE,QAAQ;QAChB,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAE,MAAM;QACd,OAAO,EAAE,OAAO;QAChB,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,YAAY,CAACD,UAAU,CAAC7B,KAAK,CAAC+B,WAAW,CAAC,CAAC,CAAC,IAAIF,UAAU,CAAC7B,KAAK;IACzE;EACF;EAEA,OAAO,eAAe;AACxB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMgC,eAAe,GAAIlI,KAAK,IAAK;EAAA,IAAAmI,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EACxC,KAAAH,gBAAA,GAAInI,KAAK,CAACG,QAAQ,cAAAgI,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3H,IAAI,cAAA4H,qBAAA,eAApBA,qBAAA,CAAsBpI,KAAK,EAAE;IAC/B,OAAOA,KAAK,CAACG,QAAQ,CAACK,IAAI,CAACR,KAAK;EAClC;EACA,KAAAqI,gBAAA,GAAIrI,KAAK,CAACG,QAAQ,cAAAkI,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7H,IAAI,cAAA8H,qBAAA,eAApBA,qBAAA,CAAsB7H,OAAO,EAAE;IACjC,OAAOT,KAAK,CAACG,QAAQ,CAACK,IAAI,CAACC,OAAO;EACpC;EACA,OAAOT,KAAK,CAACS,OAAO,IAAI,4BAA4B;AACtD,CAAC;AAED,eAAexB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}