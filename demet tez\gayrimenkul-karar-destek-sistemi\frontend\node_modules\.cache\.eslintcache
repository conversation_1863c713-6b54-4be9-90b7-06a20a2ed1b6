[{"C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\pages\\HomePage.js": "3", "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\pages\\AnalysisPage.js": "4", "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\components\\Navigation.js": "5", "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\pages\\PredictionPage.js": "6", "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\pages\\PropertiesPage.js": "7", "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\services\\api.js": "8"}, {"size": 1289, "mtime": 1748348210000, "results": "9", "hashOfConfig": "10"}, {"size": 2037, "mtime": 1748348415000, "results": "11", "hashOfConfig": "10"}, {"size": 4004, "mtime": 1749539692839, "results": "12", "hashOfConfig": "10"}, {"size": 13002, "mtime": 1748893209844, "results": "13", "hashOfConfig": "10"}, {"size": 1696, "mtime": 1748348489000, "results": "14", "hashOfConfig": "10"}, {"size": 16204, "mtime": 1748893543439, "results": "15", "hashOfConfig": "10"}, {"size": 23532, "mtime": 1749034322609, "results": "16", "hashOfConfig": "10"}, {"size": 15078, "mtime": 1749538218652, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "x6scft", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\pages\\AnalysisPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\components\\Navigation.js", [], [], "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\pages\\PredictionPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\pages\\PropertiesPage.js", ["42", "43", "44", "45", "46"], [], "C:\\Users\\<USER>\\Desktop\\demet tez\\demet tez\\gayrimenkul-karar-destek-sistemi\\frontend\\src\\services\\api.js", [], [], {"ruleId": "47", "severity": 1, "message": "48", "line": 12, "column": 3, "nodeType": "49", "messageId": "50", "endLine": 12, "endColumn": 14}, {"ruleId": "47", "severity": 1, "message": "51", "line": 13, "column": 3, "nodeType": "49", "messageId": "50", "endLine": 13, "endColumn": 13}, {"ruleId": "47", "severity": 1, "message": "52", "line": 14, "column": 3, "nodeType": "49", "messageId": "50", "endLine": 14, "endColumn": 9}, {"ruleId": "47", "severity": 1, "message": "53", "line": 46, "column": 10, "nodeType": "49", "messageId": "50", "endLine": 46, "endColumn": 21}, {"ruleId": "47", "severity": 1, "message": "54", "line": 312, "column": 9, "nodeType": "49", "messageId": "50", "endLine": 312, "endColumn": 29}, "no-unused-vars", "'FormControl' is defined but never used.", "Identifier", "unusedVar", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'currentPage' is assigned a value but never used.", "'getPropertyTypeColor' is assigned a value but never used."]