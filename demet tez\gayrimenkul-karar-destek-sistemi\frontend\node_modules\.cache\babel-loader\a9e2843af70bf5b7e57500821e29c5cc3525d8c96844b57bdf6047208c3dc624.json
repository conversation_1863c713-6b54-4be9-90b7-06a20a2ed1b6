{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demet tez\\\\demet tez\\\\gayrimenkul-karar-destek-sistemi\\\\frontend\\\\src\\\\pages\\\\PropertiesPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Grid, Card, CardContent, Typography, TextField, Button, Box, Alert, CircularProgress, FormControl, InputLabel, Select, MenuItem, Chip, Pagination, Divider } from '@mui/material';\nimport { Search as SearchIcon, FilterList as FilterIcon, Home as HomeIcon, LocationOn as LocationIcon } from '@mui/icons-material';\nimport { filterProperties, getCities, formatPrice } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PropertiesPage = () => {\n  _s();\n  const [filters, setFilters] = useState({\n    min_price: '',\n    max_price: '',\n    city: '',\n    min_rooms: '',\n    max_rooms: '',\n    min_area: '',\n    max_area: '',\n    limit: 20\n  });\n  const [properties, setProperties] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [totalCount, setTotalCount] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pagination, setPagination] = useState({\n    current_page: 1,\n    total_pages: 1,\n    page_size: 20,\n    has_next: false,\n    has_prev: false\n  });\n\n  // 9 KATEGORİLİ YATIRIM UYGUNLUK SEÇENEKLERİ\n  const investmentCategories = [{\n    value: '',\n    label: 'Tümü'\n  }, {\n    value: 'Çok Uygun',\n    label: '🟢 Çok Uygun'\n  }, {\n    value: 'Uygun',\n    label: '✅ Uygun'\n  }, {\n    value: 'Makul Fiyat',\n    label: '👍 Makul Fiyat'\n  }, {\n    value: 'Kısmen Uygun',\n    label: '🟡 Kısmen Uygun'\n  }, {\n    value: 'Orta Seviye',\n    label: '🟠 Orta Seviye'\n  }, {\n    value: 'Değerlendirilmeli',\n    label: '⚠️ Değerlendirilmeli'\n  }, {\n    value: 'Uygun Değil',\n    label: '❌ Uygun Değil'\n  }, {\n    value: 'Pahalı',\n    label: '🔴 Pahalı'\n  }, {\n    value: 'Çok Pahalı',\n    label: '🔴 Çok Pahalı'\n  }, {\n    value: 'Belirtilmemiş',\n    label: '❓ Belirtilmemiş'\n  }];\n  const loadCities = async () => {\n    try {\n      const response = await getCities();\n      console.log('🏙️ Cities response:', response);\n      console.log('🏙️ Response type:', typeof response);\n      console.log('🏙️ Response keys:', Object.keys(response));\n\n      // API response yapısını kontrol et - çoklu format desteği\n      let citiesData = [];\n      if (response.cities && Array.isArray(response.cities)) {\n        citiesData = response.cities;\n        console.log('✅ Cities found in response.cities:', citiesData.length);\n      } else if (response.data && Array.isArray(response.data)) {\n        citiesData = response.data;\n        console.log('✅ Cities found in response.data:', citiesData.length);\n      } else if (response.data && response.data.cities && Array.isArray(response.data.cities)) {\n        citiesData = response.data.cities;\n        console.log('✅ Cities found in response.data.cities:', citiesData.length);\n      } else if (Array.isArray(response)) {\n        citiesData = response;\n        console.log('✅ Cities found in response array:', citiesData.length);\n      } else {\n        console.warn('⚠️ Unexpected response format, using fallback');\n        citiesData = [];\n      }\n      console.log('📋 Raw cities data:', citiesData.slice(0, 10));\n\n      // Şehir listesini formatla - sadece string değerler\n      const formattedCities = citiesData.map(city => {\n        if (typeof city === 'string') {\n          return city.toLowerCase();\n        } else if (typeof city === 'object' && city.value) {\n          return city.value.toLowerCase();\n        } else if (typeof city === 'object' && city.label) {\n          return city.label.toLowerCase();\n        } else {\n          return String(city).toLowerCase();\n        }\n      }).filter(city => city && city.trim()); // Boş değerleri filtrele\n\n      console.log(`✅ ${formattedCities.length} şehir formatlandı:`, formattedCities.slice(0, 10));\n      setCities(formattedCities);\n    } catch (err) {\n      console.error('❌ Şehirler yüklenemedi:', err);\n      // Fallback şehir listesi - sadece string array\n      const fallbackCities = ['adana', 'ankara', 'antalya', 'bursa', 'istanbul', 'izmir', 'konya'];\n      console.log('⚠️ Fallback şehir listesi kullanılıyor:', fallbackCities);\n      setCities(fallbackCities);\n    }\n  };\n  const handleFilterChange = (field, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleSearch = useCallback(async (page = 1) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Boş filtreleri temizle ve sayfa parametresini ekle\n      const cleanFilters = Object.entries(filters).reduce((acc, [key, value]) => {\n        if (value !== '' && value !== null && value !== undefined) {\n          // Frontend'deki investment_category'yi API'nin beklediği investment_suitability'ye çevir\n          if (key === 'investment_category') {\n            acc['investment_suitability'] = value;\n          } else {\n            acc[key] = value;\n          }\n        }\n        return acc;\n      }, {});\n\n      // Sayfa parametresini ekle\n      cleanFilters.page = page;\n      console.log('🔍 Sending filters to API:', cleanFilters);\n      const response = await filterProperties(cleanFilters);\n      console.log('✅ API response:', response);\n\n      // Response yapısını kontrol et\n      const responseData = response.data || response;\n      if (!responseData.success) {\n        throw new Error(responseData.error || 'API hatası');\n      }\n      setProperties(responseData.properties || []);\n      setTotalCount(responseData.total_count || 0);\n      setCurrentPage(page);\n\n      // Pagination bilgilerini güncelle\n      if (responseData.pagination) {\n        setPagination(responseData.pagination);\n      } else {\n        // Fallback pagination hesaplama\n        const totalPages = Math.ceil((responseData.total_count || 0) / filters.limit);\n        setPagination({\n          current_page: page,\n          total_pages: totalPages,\n          page_size: filters.limit,\n          has_next: page < totalPages,\n          has_prev: page > 1\n        });\n      }\n    } catch (err) {\n      console.error('❌ Filter error:', err);\n      setError('İlanlar yüklenirken hata oluştu: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [filters]);\n  useEffect(() => {\n    loadCities();\n    // İlk yüklemede otomatik arama KAPALI - kullanıcı \"Ara\" butonuna basmalı\n    // handleSearch(); // DEVRE DIŞI - Otomatik arama engellendi\n  }, []); // handleSearch dependency kaldırıldı - otomatik arama engellendi\n\n  const resetFilters = () => {\n    setFilters({\n      min_price: '',\n      max_price: '',\n      city: '',\n      min_rooms: '',\n      max_rooms: '',\n      min_area: '',\n      max_area: '',\n      limit: 20\n    });\n    setCurrentPage(1);\n  };\n\n  // Sayfa değişikliği handler'ı\n  const handlePageChange = (event, page) => {\n    console.log('Page changed to:', page);\n    handleSearch(page);\n  };\n\n  // 9 KATEGORİLİ YATIRIM UYGUNLUK RENK VE STİL KONTROLÜ\n  const getInvestmentStyle = yatirimUygunluk => {\n    if (!yatirimUygunluk) {\n      return {\n        color: 'default',\n        backgroundColor: '#f5f5f5',\n        textColor: '#666',\n        icon: '❓'\n      };\n    }\n    const value = yatirimUygunluk.toLowerCase();\n\n    // 9 KATEGORİLİ DETAYLI RENK KODLAMASI\n    if (value.includes('çok uygun')) {\n      return {\n        color: 'success',\n        backgroundColor: '#1b5e20',\n        // Koyu yeşil\n        textColor: '#ffffff',\n        icon: '🟢'\n      };\n    } else if (value.includes('uygun') && !value.includes('değil') && !value.includes('kısmen')) {\n      return {\n        color: 'success',\n        backgroundColor: '#2e7d32',\n        // Yeşil\n        textColor: '#ffffff',\n        icon: '✅'\n      };\n    } else if (value.includes('makul')) {\n      return {\n        color: 'success',\n        backgroundColor: '#388e3c',\n        // Açık yeşil\n        textColor: '#ffffff',\n        icon: '👍'\n      };\n    } else if (value.includes('kısmen')) {\n      return {\n        color: 'warning',\n        backgroundColor: '#f57c00',\n        // Turuncu\n        textColor: '#ffffff',\n        icon: '🟡'\n      };\n    } else if (value.includes('orta seviye') || value.includes('orta')) {\n      return {\n        color: 'warning',\n        backgroundColor: '#ef6c00',\n        // Koyu turuncu\n        textColor: '#ffffff',\n        icon: '🟠'\n      };\n    } else if (value.includes('değerlendirilmeli')) {\n      return {\n        color: 'warning',\n        backgroundColor: '#ff9800',\n        // Sarı-turuncu\n        textColor: '#ffffff',\n        icon: '⚠️'\n      };\n    } else if (value.includes('uygun değil') || value.includes('değil')) {\n      return {\n        color: 'error',\n        backgroundColor: '#d32f2f',\n        // Kırmızı\n        textColor: '#ffffff',\n        icon: '❌'\n      };\n    } else if (value.includes('pahalı')) {\n      return {\n        color: 'error',\n        backgroundColor: '#c62828',\n        // Koyu kırmızı\n        textColor: '#ffffff',\n        icon: '🔴'\n      };\n    } else if (value.includes('belirtilmemiş') || value.includes('bilinmiyor') || value.includes('belirsiz')) {\n      return {\n        color: 'default',\n        backgroundColor: '#9e9e9e',\n        // Gri\n        textColor: '#ffffff',\n        icon: '❓'\n      };\n    } else {\n      return {\n        color: 'default',\n        backgroundColor: '#757575',\n        // Koyu gri\n        textColor: '#ffffff',\n        icon: '❓'\n      };\n    }\n  };\n\n  // Geriye uyumluluk için eski fonksiyon\n  const getPropertyTypeColor = yatirimUygunluk => {\n    return getInvestmentStyle(yatirimUygunluk).color;\n  };\n\n  // Güvenli değer gösterme fonksiyonları\n  const safeValue = (value, defaultValue = 'Belirtilmemiş') => {\n    if (value === null || value === undefined || value === '') {\n      return defaultValue;\n    }\n    return value;\n  };\n  const safeNumber = (value, defaultValue = 0) => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return defaultValue;\n    }\n    return Number(value);\n  };\n  const safeCityName = city => {\n    if (!city) return 'Belirtilmemiş';\n    return city.charAt(0).toUpperCase() + city.slice(1);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h1\",\n      gutterBottom: true,\n      children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n        sx: {\n          mr: 2,\n          verticalAlign: 'middle'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), \"Gayrimenkul \\u0130lan Arama\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 4\n      },\n      children: \"Ak\\u0131ll\\u0131 filtreleme ile istedi\\u011Finiz gayrimenkul ilanlar\\u0131n\\u0131 bulun\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(FilterIcon, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), \"Arama Filtreleri\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Min Fiyat (TL)\",\n              type: \"number\",\n              value: filters.min_price,\n              onChange: e => handleFilterChange('min_price', e.target.value),\n              inputProps: {\n                min: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Max Fiyat (TL)\",\n              type: \"number\",\n              value: filters.max_price,\n              onChange: e => handleFilterChange('max_price', e.target.value),\n              inputProps: {\n                min: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              label: `Şehir (${cities.length} şehir)`,\n              fullWidth: true,\n              value: filters.city,\n              onChange: e => handleFilterChange('city', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"T\\xFCm\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this), cities.map(city => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: city,\n                children: city.charAt(0).toUpperCase() + city.slice(1)\n              }, city, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              label: \"Min Oda Say\\u0131s\\u0131\",\n              fullWidth: true,\n              value: filters.min_rooms,\n              onChange: e => handleFilterChange('min_rooms', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"T\\xFCm\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 19\n              }, this), [1, 2, 3, 4, 5, 6].map(num => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: num,\n                children: num\n              }, num, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              label: \"Max Oda Say\\u0131s\\u0131\",\n              fullWidth: true,\n              value: filters.max_rooms,\n              onChange: e => handleFilterChange('max_rooms', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"T\\xFCm\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this), [1, 2, 3, 4, 5, 6].map(num => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: num,\n                children: num\n              }, num, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Min Metrekare\",\n              type: \"number\",\n              value: filters.min_area,\n              onChange: e => handleFilterChange('min_area', e.target.value),\n              inputProps: {\n                min: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Max Metrekare\",\n              type: \"number\",\n              value: filters.max_area,\n              onChange: e => handleFilterChange('max_area', e.target.value),\n              inputProps: {\n                min: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              label: \"Sonu\\xE7 Say\\u0131s\\u0131\",\n              fullWidth: true,\n              value: filters.limit,\n              onChange: e => handleFilterChange('limit', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 10,\n                children: \"10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 20,\n                children: \"20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 50,\n                children: \"50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: 100,\n                children: \"100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: () => handleSearch(1),\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 69\n            }, this),\n            children: loading ? 'Aranıyor...' : 'Ara'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: resetFilters,\n            disabled: loading,\n            children: \"Filtreleri Temizle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 9\n    }, this), !loading && properties.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [totalCount, \" ilan bulundu\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: properties.map((property, index) => {\n        // Veri formatını normalize et - hem eski hem yeni format desteği\n        const normalizedProperty = {\n          id: property.id || index,\n          fiyat: property.Fiyat || property.fiyat || 0,\n          net_metrekare: property.Net_Metrekare || property.net_metrekare || 0,\n          oda_sayisi: property.Oda_Sayısı || property.oda_sayisi || property.Oda_Sayisi || 0,\n          sehir: property.Şehir || property.sehir || property.city || 'Belirtilmemiş',\n          bulundugu_kat: property.Bulunduğu_Kat || property.bulundugu_kat || property.Bulundugu_Kat || 'Belirtilmemiş',\n          binanin_yasi: property.Binanın_Yaşı || property.binanin_yasi || property.Binanin_Yasi || 'Belirtilmemiş',\n          isitma_tipi: property.Isıtma_Tipi || property.isitma_tipi || property.Isitma_Tipi || 'Belirtilmemiş',\n          esya_durumu: property.Eşya_Durumu || property.esya_durumu || property.Esya_Durumu || 'Belirtilmemiş',\n          yatirim_uygunluk: property.Yatırıma_Uygunluk || property.yatirim_uygunluk || property.Yatirim_Uygunluk || 'Belirtilmemiş',\n          banyo_sayisi: property.Banyo_Sayısı || property.banyo_sayisi || property.Banyo_Sayisi || 1,\n          m2_basi_fiyat: property.m2_basi_fiyat || (property.fiyat && property.net_metrekare ? property.fiyat / property.net_metrekare : 0)\n        };\n        console.log(`🏠 İlan ${index + 1} normalize edildi:`, normalizedProperty);\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          lg: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flexGrow: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                color: \"primary\",\n                gutterBottom: true,\n                children: formatPrice(safeNumber(normalizedProperty.fiyat))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), normalizedProperty.m2_basi_fiyat > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                display: \"block\",\n                gutterBottom: true,\n                children: [formatPrice(normalizedProperty.m2_basi_fiyat), \"/m\\xB2\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: [/*#__PURE__*/_jsxDEV(HomeIcon, {\n                    sx: {\n                      fontSize: 16,\n                      mr: 0.5,\n                      verticalAlign: 'middle'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 23\n                  }, this), safeNumber(normalizedProperty.net_metrekare), \" m\\xB2 \\u2022 \", safeNumber(normalizedProperty.oda_sayisi), \" oda\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: [/*#__PURE__*/_jsxDEV(LocationIcon, {\n                    sx: {\n                      fontSize: 16,\n                      mr: 0.5,\n                      verticalAlign: 'middle'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 23\n                  }, this), safeCityName(normalizedProperty.sehir)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  color: \"text.secondary\",\n                  children: [\"Kat: \", safeValue(normalizedProperty.bulundugu_kat)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  color: \"text.secondary\",\n                  children: [\"Bina Ya\\u015F\\u0131: \", safeValue(normalizedProperty.binanin_yasi)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  color: \"text.secondary\",\n                  children: [\"Is\\u0131tma: \", safeValue(normalizedProperty.isitma_tipi)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  color: \"text.secondary\",\n                  children: [\"E\\u015Fya: \", safeValue(normalizedProperty.esya_durumu)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 21\n                }, this), normalizedProperty.banyo_sayisi > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  color: \"text.secondary\",\n                  children: [\"Banyo: \", normalizedProperty.banyo_sayisi]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 'auto'\n                },\n                children: (() => {\n                  const investmentStyle = getInvestmentStyle(normalizedProperty.yatirim_uygunluk);\n                  const investmentValue = safeValue(normalizedProperty.yatirim_uygunluk, 'Belirtilmemiş');\n                  return /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${investmentStyle.icon} ${investmentValue}`,\n                    sx: {\n                      backgroundColor: investmentStyle.backgroundColor,\n                      color: investmentStyle.textColor,\n                      fontWeight: 'bold',\n                      fontSize: '0.75rem',\n                      '& .MuiChip-label': {\n                        color: investmentStyle.textColor\n                      }\n                    },\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 25\n                  }, this);\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this)\n        }, normalizedProperty.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 7\n    }, this), !loading && properties.length === 0 && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n          sx: {\n            fontSize: 80,\n            color: 'grey.300',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          children: \"Arama kriterlerinize uygun ilan bulunamad\\u0131\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Filtreleri de\\u011Fi\\u015Ftirerek tekrar deneyin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 9\n    }, this), !loading && properties.length > 0 && pagination.total_pages > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 4,\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Sayfa \", pagination.current_page, \" / \", pagination.total_pages, \"(\", totalCount, \" sonu\\xE7tan \", (pagination.current_page - 1) * pagination.page_size + 1, \"-\", Math.min(pagination.current_page * pagination.page_size, totalCount), \" aras\\u0131 g\\xF6steriliyor)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n          count: pagination.total_pages,\n          page: pagination.current_page,\n          onChange: handlePageChange,\n          color: \"primary\",\n          size: \"large\",\n          showFirstButton: true,\n          showLastButton: true,\n          siblingCount: 2,\n          boundaryCount: 1\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n};\n_s(PropertiesPage, \"hBGUFC9/ugccc4lBhMC7Fx3qaNI=\");\n_c = PropertiesPage;\nexport default PropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"PropertiesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "TextField", "<PERSON><PERSON>", "Box", "<PERSON><PERSON>", "CircularProgress", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "Pagination", "Divider", "Search", "SearchIcon", "FilterList", "FilterIcon", "Home", "HomeIcon", "LocationOn", "LocationIcon", "filterProperties", "getCities", "formatPrice", "jsxDEV", "_jsxDEV", "PropertiesPage", "_s", "filters", "setFilters", "min_price", "max_price", "city", "min_rooms", "max_rooms", "min_area", "max_area", "limit", "properties", "setProperties", "cities", "setCities", "loading", "setLoading", "error", "setError", "totalCount", "setTotalCount", "currentPage", "setCurrentPage", "pagination", "setPagination", "current_page", "total_pages", "page_size", "has_next", "has_prev", "investmentCategories", "value", "label", "loadCities", "response", "console", "log", "Object", "keys", "citiesData", "Array", "isArray", "length", "data", "warn", "slice", "formattedCities", "map", "toLowerCase", "String", "filter", "trim", "err", "fallbackCities", "handleFilterChange", "field", "prev", "handleSearch", "page", "cleanFilters", "entries", "reduce", "acc", "key", "undefined", "responseData", "success", "Error", "total_count", "totalPages", "Math", "ceil", "message", "resetFilters", "handlePageChange", "event", "getInvestmentStyle", "yatirimUygunluk", "color", "backgroundColor", "textColor", "icon", "includes", "getPropertyTypeColor", "safeValue", "defaultValue", "safeNumber", "isNaN", "Number", "safeCityName", "char<PERSON>t", "toUpperCase", "children", "variant", "component", "gutterBottom", "sx", "mr", "verticalAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "container", "spacing", "item", "xs", "sm", "md", "fullWidth", "type", "onChange", "e", "target", "inputProps", "min", "select", "num", "mt", "display", "gap", "onClick", "disabled", "startIcon", "size", "severity", "property", "index", "normalizedProperty", "id", "fiyat", "<PERSON><PERSON><PERSON>", "net_metrekare", "Net_Metrekare", "oda_sayisi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Şehir", "bulundugu_kat", "Bulunduğu_Kat", "Bulundugu_Kat", "binanin_yasi", "Binanın_Yaşı", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isitma_tipi", "Isıtma_Tipi", "Isitma_Tipi", "esya_durumu", "<PERSON><PERSON><PERSON>_<PERSON>", "Esya_Durumu", "ya<PERSON><PERSON>_u<PERSON>luk", "Yatırıma_Uygunluk", "<PERSON><PERSON><PERSON>_Uygunluk", "banyo_sayisi", "Banyo_Sayısı", "Ban<PERSON>_Say<PERSON>", "m2_basi_fiyat", "lg", "height", "flexDirection", "flexGrow", "fontSize", "my", "investmentStyle", "investmentValue", "fontWeight", "textAlign", "py", "justifyContent", "alignItems", "count", "showFirstButton", "showLastButton", "siblingCount", "boundaryCount", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/demet tez/demet tez/gayrimenkul-karar-destek-sistemi/frontend/src/pages/PropertiesPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  Typography,\n  TextField,\n  Button,\n  Box,\n  Alert,\n  CircularProgress,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  Pagination,\n  Divider\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  Home as HomeIcon,\n  LocationOn as LocationIcon\n} from '@mui/icons-material';\nimport { filterProperties, getCities, formatPrice } from '../services/api';\n\nconst PropertiesPage = () => {\n  const [filters, setFilters] = useState({\n    min_price: '',\n    max_price: '',\n    city: '',\n    min_rooms: '',\n    max_rooms: '',\n    min_area: '',\n    max_area: '',\n    limit: 20\n  });\n\n  const [properties, setProperties] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [totalCount, setTotalCount] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pagination, setPagination] = useState({\n    current_page: 1,\n    total_pages: 1,\n    page_size: 20,\n    has_next: false,\n    has_prev: false\n  });\n\n  // 9 KATEGORİLİ YATIRIM UYGUNLUK SEÇENEKLERİ\n  const investmentCategories = [\n    { value: '', label: 'Tümü' },\n    { value: 'Çok Uygun', label: '🟢 Çok Uygun' },\n    { value: 'Uygun', label: '✅ Uygun' },\n    { value: 'Makul Fiyat', label: '👍 Makul Fiyat' },\n    { value: 'Kısmen Uygun', label: '🟡 Kısmen Uygun' },\n    { value: 'Orta Seviye', label: '🟠 Orta Seviye' },\n    { value: 'Değerlendirilmeli', label: '⚠️ Değerlendirilmeli' },\n    { value: 'Uygun Değil', label: '❌ Uygun Değil' },\n    { value: 'Pahalı', label: '🔴 Pahalı' },\n    { value: 'Çok Pahalı', label: '🔴 Çok Pahalı' },\n    { value: 'Belirtilmemiş', label: '❓ Belirtilmemiş' }\n  ];\n\n  const loadCities = async () => {\n    try {\n      const response = await getCities();\n      console.log('🏙️ Cities response:', response);\n      console.log('🏙️ Response type:', typeof response);\n      console.log('🏙️ Response keys:', Object.keys(response));\n\n      // API response yapısını kontrol et - çoklu format desteği\n      let citiesData = [];\n\n      if (response.cities && Array.isArray(response.cities)) {\n        citiesData = response.cities;\n        console.log('✅ Cities found in response.cities:', citiesData.length);\n      } else if (response.data && Array.isArray(response.data)) {\n        citiesData = response.data;\n        console.log('✅ Cities found in response.data:', citiesData.length);\n      } else if (response.data && response.data.cities && Array.isArray(response.data.cities)) {\n        citiesData = response.data.cities;\n        console.log('✅ Cities found in response.data.cities:', citiesData.length);\n      } else if (Array.isArray(response)) {\n        citiesData = response;\n        console.log('✅ Cities found in response array:', citiesData.length);\n      } else {\n        console.warn('⚠️ Unexpected response format, using fallback');\n        citiesData = [];\n      }\n\n      console.log('📋 Raw cities data:', citiesData.slice(0, 10));\n\n      // Şehir listesini formatla - sadece string değerler\n      const formattedCities = citiesData.map(city => {\n        if (typeof city === 'string') {\n          return city.toLowerCase();\n        } else if (typeof city === 'object' && city.value) {\n          return city.value.toLowerCase();\n        } else if (typeof city === 'object' && city.label) {\n          return city.label.toLowerCase();\n        } else {\n          return String(city).toLowerCase();\n        }\n      }).filter(city => city && city.trim()); // Boş değerleri filtrele\n\n      console.log(`✅ ${formattedCities.length} şehir formatlandı:`, formattedCities.slice(0, 10));\n      setCities(formattedCities);\n    } catch (err) {\n      console.error('❌ Şehirler yüklenemedi:', err);\n      // Fallback şehir listesi - sadece string array\n      const fallbackCities = ['adana', 'ankara', 'antalya', 'bursa', 'istanbul', 'izmir', 'konya'];\n      console.log('⚠️ Fallback şehir listesi kullanılıyor:', fallbackCities);\n      setCities(fallbackCities);\n    }\n  };\n\n  const handleFilterChange = (field, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSearch = useCallback(async (page = 1) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Boş filtreleri temizle ve sayfa parametresini ekle\n      const cleanFilters = Object.entries(filters).reduce((acc, [key, value]) => {\n        if (value !== '' && value !== null && value !== undefined) {\n          // Frontend'deki investment_category'yi API'nin beklediği investment_suitability'ye çevir\n          if (key === 'investment_category') {\n            acc['investment_suitability'] = value;\n          } else {\n            acc[key] = value;\n          }\n        }\n        return acc;\n      }, {});\n\n      // Sayfa parametresini ekle\n      cleanFilters.page = page;\n\n      console.log('🔍 Sending filters to API:', cleanFilters);\n      const response = await filterProperties(cleanFilters);\n      console.log('✅ API response:', response);\n\n      // Response yapısını kontrol et\n      const responseData = response.data || response;\n\n      if (!responseData.success) {\n        throw new Error(responseData.error || 'API hatası');\n      }\n\n      setProperties(responseData.properties || []);\n      setTotalCount(responseData.total_count || 0);\n      setCurrentPage(page);\n\n      // Pagination bilgilerini güncelle\n      if (responseData.pagination) {\n        setPagination(responseData.pagination);\n      } else {\n        // Fallback pagination hesaplama\n        const totalPages = Math.ceil((responseData.total_count || 0) / filters.limit);\n        setPagination({\n          current_page: page,\n          total_pages: totalPages,\n          page_size: filters.limit,\n          has_next: page < totalPages,\n          has_prev: page > 1\n        });\n      }\n    } catch (err) {\n      console.error('❌ Filter error:', err);\n      setError('İlanlar yüklenirken hata oluştu: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [filters]);\n\n  useEffect(() => {\n    loadCities();\n    // İlk yüklemede otomatik arama KAPALI - kullanıcı \"Ara\" butonuna basmalı\n    // handleSearch(); // DEVRE DIŞI - Otomatik arama engellendi\n  }, []); // handleSearch dependency kaldırıldı - otomatik arama engellendi\n\n  const resetFilters = () => {\n    setFilters({\n      min_price: '',\n      max_price: '',\n      city: '',\n      min_rooms: '',\n      max_rooms: '',\n      min_area: '',\n      max_area: '',\n      limit: 20\n    });\n    setCurrentPage(1);\n  };\n\n  // Sayfa değişikliği handler'ı\n  const handlePageChange = (event, page) => {\n    console.log('Page changed to:', page);\n    handleSearch(page);\n  };\n\n  // 9 KATEGORİLİ YATIRIM UYGUNLUK RENK VE STİL KONTROLÜ\n  const getInvestmentStyle = (yatirimUygunluk) => {\n    if (!yatirimUygunluk) {\n      return {\n        color: 'default',\n        backgroundColor: '#f5f5f5',\n        textColor: '#666',\n        icon: '❓'\n      };\n    }\n\n    const value = yatirimUygunluk.toLowerCase();\n\n    // 9 KATEGORİLİ DETAYLI RENK KODLAMASI\n    if (value.includes('çok uygun')) {\n      return {\n        color: 'success',\n        backgroundColor: '#1b5e20', // Koyu yeşil\n        textColor: '#ffffff',\n        icon: '🟢'\n      };\n    }\n    else if (value.includes('uygun') && !value.includes('değil') && !value.includes('kısmen')) {\n      return {\n        color: 'success',\n        backgroundColor: '#2e7d32', // Yeşil\n        textColor: '#ffffff',\n        icon: '✅'\n      };\n    }\n    else if (value.includes('makul')) {\n      return {\n        color: 'success',\n        backgroundColor: '#388e3c', // Açık yeşil\n        textColor: '#ffffff',\n        icon: '👍'\n      };\n    }\n    else if (value.includes('kısmen')) {\n      return {\n        color: 'warning',\n        backgroundColor: '#f57c00', // Turuncu\n        textColor: '#ffffff',\n        icon: '🟡'\n      };\n    }\n    else if (value.includes('orta seviye') || value.includes('orta')) {\n      return {\n        color: 'warning',\n        backgroundColor: '#ef6c00', // Koyu turuncu\n        textColor: '#ffffff',\n        icon: '🟠'\n      };\n    }\n    else if (value.includes('değerlendirilmeli')) {\n      return {\n        color: 'warning',\n        backgroundColor: '#ff9800', // Sarı-turuncu\n        textColor: '#ffffff',\n        icon: '⚠️'\n      };\n    }\n    else if (value.includes('uygun değil') || value.includes('değil')) {\n      return {\n        color: 'error',\n        backgroundColor: '#d32f2f', // Kırmızı\n        textColor: '#ffffff',\n        icon: '❌'\n      };\n    }\n    else if (value.includes('pahalı')) {\n      return {\n        color: 'error',\n        backgroundColor: '#c62828', // Koyu kırmızı\n        textColor: '#ffffff',\n        icon: '🔴'\n      };\n    }\n    else if (value.includes('belirtilmemiş') || value.includes('bilinmiyor') || value.includes('belirsiz')) {\n      return {\n        color: 'default',\n        backgroundColor: '#9e9e9e', // Gri\n        textColor: '#ffffff',\n        icon: '❓'\n      };\n    }\n    else {\n      return {\n        color: 'default',\n        backgroundColor: '#757575', // Koyu gri\n        textColor: '#ffffff',\n        icon: '❓'\n      };\n    }\n  };\n\n  // Geriye uyumluluk için eski fonksiyon\n  const getPropertyTypeColor = (yatirimUygunluk) => {\n    return getInvestmentStyle(yatirimUygunluk).color;\n  };\n\n  // Güvenli değer gösterme fonksiyonları\n  const safeValue = (value, defaultValue = 'Belirtilmemiş') => {\n    if (value === null || value === undefined || value === '') {\n      return defaultValue;\n    }\n    return value;\n  };\n\n  const safeNumber = (value, defaultValue = 0) => {\n    if (value === null || value === undefined || isNaN(value)) {\n      return defaultValue;\n    }\n    return Number(value);\n  };\n\n  const safeCityName = (city) => {\n    if (!city) return 'Belirtilmemiş';\n    return city.charAt(0).toUpperCase() + city.slice(1);\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n        <SearchIcon sx={{ mr: 2, verticalAlign: 'middle' }} />\n        Gayrimenkul İlan Arama\n      </Typography>\n\n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n        Akıllı filtreleme ile istediğiniz gayrimenkul ilanlarını bulun\n      </Typography>\n\n      {/* Filtreler */}\n      <Card sx={{ mb: 4 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            <FilterIcon sx={{ mr: 1, verticalAlign: 'middle' }} />\n            Arama Filtreleri\n          </Typography>\n\n          <Grid container spacing={3}>\n            {/* Fiyat Aralığı */}\n            <Grid item xs={12} sm={6} md={3}>\n              <TextField\n                fullWidth\n                label=\"Min Fiyat (TL)\"\n                type=\"number\"\n                value={filters.min_price}\n                onChange={(e) => handleFilterChange('min_price', e.target.value)}\n                inputProps={{ min: 0 }}\n              />\n            </Grid>\n\n            <Grid item xs={12} sm={6} md={3}>\n              <TextField\n                fullWidth\n                label=\"Max Fiyat (TL)\"\n                type=\"number\"\n                value={filters.max_price}\n                onChange={(e) => handleFilterChange('max_price', e.target.value)}\n                inputProps={{ min: 0 }}\n              />\n            </Grid>\n\n            {/* Şehir */}\n            <Grid item xs={12} sm={6} md={3}>\n              <TextField\n                select\n                label={`Şehir (${cities.length} şehir)`}\n                fullWidth\n                value={filters.city}\n                onChange={(e) => handleFilterChange('city', e.target.value)}\n              >\n                  <MenuItem value=\"\">Tümü</MenuItem>\n                  {cities.map(city => (\n                    <MenuItem key={city} value={city}>\n                      {city.charAt(0).toUpperCase() + city.slice(1)}\n                    </MenuItem>\n                  ))}\n              </TextField>\n            </Grid>\n\n            {/* Oda Sayısı */}\n            <Grid item xs={12} sm={6} md={3}>\n              <TextField\n                select\n                label=\"Min Oda Sayısı\"\n                fullWidth\n                value={filters.min_rooms}\n                onChange={(e) => handleFilterChange('min_rooms', e.target.value)}\n              >\n                  <MenuItem value=\"\">Tümü</MenuItem>\n                  {[1, 2, 3, 4, 5, 6].map(num => (\n                    <MenuItem key={num} value={num}>{num}</MenuItem>\n                  ))}\n              </TextField>\n            </Grid>\n\n            <Grid item xs={12} sm={6} md={3}>\n              <TextField\n                select\n                label=\"Max Oda Sayısı\"\n                fullWidth\n                value={filters.max_rooms}\n                onChange={(e) => handleFilterChange('max_rooms', e.target.value)}\n              >\n                  <MenuItem value=\"\">Tümü</MenuItem>\n                  {[1, 2, 3, 4, 5, 6].map(num => (\n                    <MenuItem key={num} value={num}>{num}</MenuItem>\n                  ))}\n              </TextField>\n            </Grid>\n\n            {/* Metrekare */}\n            <Grid item xs={12} sm={6} md={3}>\n              <TextField\n                fullWidth\n                label=\"Min Metrekare\"\n                type=\"number\"\n                value={filters.min_area}\n                onChange={(e) => handleFilterChange('min_area', e.target.value)}\n                inputProps={{ min: 0 }}\n              />\n            </Grid>\n\n            <Grid item xs={12} sm={6} md={3}>\n              <TextField\n                fullWidth\n                label=\"Max Metrekare\"\n                type=\"number\"\n                value={filters.max_area}\n                onChange={(e) => handleFilterChange('max_area', e.target.value)}\n                inputProps={{ min: 0 }}\n              />\n            </Grid>\n            {/* Sonuç Limiti */}\n            <Grid item xs={12} sm={6} md={3}>\n              <TextField\n                select\n                label=\"Sonuç Sayısı\"\n                fullWidth\n                value={filters.limit}\n                onChange={(e) => handleFilterChange('limit', e.target.value)}\n              >\n                  <MenuItem value={10}>10</MenuItem>\n                  <MenuItem value={20}>20</MenuItem>\n                  <MenuItem value={50}>50</MenuItem>\n                  <MenuItem value={100}>100</MenuItem>\n              </TextField>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              onClick={() => handleSearch(1)}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}\n            >\n              {loading ? 'Aranıyor...' : 'Ara'}\n            </Button>\n\n            <Button\n              variant=\"outlined\"\n              onClick={resetFilters}\n              disabled={loading}\n            >\n              Filtreleri Temizle\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* Hata Mesajı */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Sonuç Sayısı */}\n      {!loading && properties.length > 0 && (\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\">\n            {totalCount} ilan bulundu\n          </Typography>\n        </Box>\n      )}\n\n      {/* İlan Listesi */}\n      <Grid container spacing={3}>\n        {properties.map((property, index) => {\n          // Veri formatını normalize et - hem eski hem yeni format desteği\n          const normalizedProperty = {\n            id: property.id || index,\n            fiyat: property.Fiyat || property.fiyat || 0,\n            net_metrekare: property.Net_Metrekare || property.net_metrekare || 0,\n            oda_sayisi: property.Oda_Sayısı || property.oda_sayisi || property.Oda_Sayisi || 0,\n            sehir: property.Şehir || property.sehir || property.city || 'Belirtilmemiş',\n            bulundugu_kat: property.Bulunduğu_Kat || property.bulundugu_kat || property.Bulundugu_Kat || 'Belirtilmemiş',\n            binanin_yasi: property.Binanın_Yaşı || property.binanin_yasi || property.Binanin_Yasi || 'Belirtilmemiş',\n            isitma_tipi: property.Isıtma_Tipi || property.isitma_tipi || property.Isitma_Tipi || 'Belirtilmemiş',\n            esya_durumu: property.Eşya_Durumu || property.esya_durumu || property.Esya_Durumu || 'Belirtilmemiş',\n            yatirim_uygunluk: property.Yatırıma_Uygunluk || property.yatirim_uygunluk || property.Yatirim_Uygunluk || 'Belirtilmemiş',\n            banyo_sayisi: property.Banyo_Sayısı || property.banyo_sayisi || property.Banyo_Sayisi || 1,\n            m2_basi_fiyat: property.m2_basi_fiyat || (property.fiyat && property.net_metrekare ? property.fiyat / property.net_metrekare : 0)\n          };\n\n          console.log(`🏠 İlan ${index + 1} normalize edildi:`, normalizedProperty);\n\n          return (\n            <Grid item xs={12} sm={6} md={4} lg={3} key={normalizedProperty.id}>\n              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n                <CardContent sx={{ flexGrow: 1 }}>\n                  {/* Fiyat */}\n                  <Typography variant=\"h5\" color=\"primary\" gutterBottom>\n                    {formatPrice(safeNumber(normalizedProperty.fiyat))}\n                  </Typography>\n\n                  {/* m² başına fiyat */}\n                  {normalizedProperty.m2_basi_fiyat > 0 && (\n                    <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" gutterBottom>\n                      {formatPrice(normalizedProperty.m2_basi_fiyat)}/m²\n                    </Typography>\n                  )}\n\n                  {/* Temel Bilgiler */}\n                  <Box sx={{ mb: 2 }}>\n                    <Typography variant=\"body1\" gutterBottom>\n                      <HomeIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />\n                      {safeNumber(normalizedProperty.net_metrekare)} m² • {safeNumber(normalizedProperty.oda_sayisi)} oda\n                    </Typography>\n\n                    <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                      <LocationIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />\n                      {safeCityName(normalizedProperty.sehir)}\n                    </Typography>\n                  </Box>\n\n                  <Divider sx={{ my: 1 }} />\n\n                  {/* Detaylar */}\n                  <Box sx={{ mb: 2 }}>\n                    <Typography variant=\"caption\" display=\"block\" color=\"text.secondary\">\n                      Kat: {safeValue(normalizedProperty.bulundugu_kat)}\n                    </Typography>\n                    <Typography variant=\"caption\" display=\"block\" color=\"text.secondary\">\n                      Bina Yaşı: {safeValue(normalizedProperty.binanin_yasi)}\n                    </Typography>\n                    <Typography variant=\"caption\" display=\"block\" color=\"text.secondary\">\n                      Isıtma: {safeValue(normalizedProperty.isitma_tipi)}\n                    </Typography>\n                    <Typography variant=\"caption\" display=\"block\" color=\"text.secondary\">\n                      Eşya: {safeValue(normalizedProperty.esya_durumu)}\n                    </Typography>\n                    {normalizedProperty.banyo_sayisi > 0 && (\n                      <Typography variant=\"caption\" display=\"block\" color=\"text.secondary\">\n                        Banyo: {normalizedProperty.banyo_sayisi}\n                      </Typography>\n                    )}\n                  </Box>\n\n                  {/* Yatırım Uygunluğu - Özelleştirilmiş Chip */}\n                  <Box sx={{ mt: 'auto' }}>\n                    {(() => {\n                      const investmentStyle = getInvestmentStyle(normalizedProperty.yatirim_uygunluk);\n                      const investmentValue = safeValue(normalizedProperty.yatirim_uygunluk, 'Belirtilmemiş');\n\n                      return (\n                        <Chip\n                          label={`${investmentStyle.icon} ${investmentValue}`}\n                          sx={{\n                            backgroundColor: investmentStyle.backgroundColor,\n                            color: investmentStyle.textColor,\n                            fontWeight: 'bold',\n                            fontSize: '0.75rem',\n                            '& .MuiChip-label': {\n                              color: investmentStyle.textColor\n                            }\n                          }}\n                          size=\"small\"\n                        />\n                      );\n                    })()}\n                  </Box>\n                </CardContent>\n              </Card>\n            </Grid>\n          );\n        })}\n      </Grid>\n\n      {/* İlan Bulunamadı */}\n      {!loading && properties.length === 0 && (\n        <Card>\n          <CardContent sx={{ textAlign: 'center', py: 8 }}>\n            <SearchIcon sx={{ fontSize: 80, color: 'grey.300', mb: 2 }} />\n            <Typography variant=\"h6\" color=\"text.secondary\">\n              Arama kriterlerinize uygun ilan bulunamadı\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Filtreleri değiştirerek tekrar deneyin\n            </Typography>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Sayfalama */}\n      {!loading && properties.length > 0 && pagination.total_pages > 1 && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4, mb: 2 }}>\n          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Sayfa {pagination.current_page} / {pagination.total_pages}\n              ({totalCount} sonuçtan {((pagination.current_page - 1) * pagination.page_size) + 1}-{Math.min(pagination.current_page * pagination.page_size, totalCount)} arası gösteriliyor)\n            </Typography>\n            <Pagination\n              count={pagination.total_pages}\n              page={pagination.current_page}\n              onChange={handlePageChange}\n              color=\"primary\"\n              size=\"large\"\n              showFirstButton\n              showLastButton\n              siblingCount={2}\n              boundaryCount={1}\n            />\n          </Box>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default PropertiesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,gBAAgB,EAChBC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,YAAY,QACrB,qBAAqB;AAC5B,SAASC,gBAAgB,EAAEC,SAAS,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC;IACrCoC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC;IAC3C0D,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,oBAAoB,GAAG,CAC3B;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC5B;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC7C;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAU,CAAC,EACpC;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACjD;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACnD;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACjD;IAAED,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAuB,CAAC,EAC7D;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAChD;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAY,CAAC,EACvC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC/C;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAkB,CAAC,CACrD;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMvC,SAAS,CAAC,CAAC;MAClCwC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,QAAQ,CAAC;MAC7CC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,OAAOF,QAAQ,CAAC;MAClDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC;;MAExD;MACA,IAAIK,UAAU,GAAG,EAAE;MAEnB,IAAIL,QAAQ,CAACrB,MAAM,IAAI2B,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACrB,MAAM,CAAC,EAAE;QACrD0B,UAAU,GAAGL,QAAQ,CAACrB,MAAM;QAC5BsB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEG,UAAU,CAACG,MAAM,CAAC;MACtE,CAAC,MAAM,IAAIR,QAAQ,CAACS,IAAI,IAAIH,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACS,IAAI,CAAC,EAAE;QACxDJ,UAAU,GAAGL,QAAQ,CAACS,IAAI;QAC1BR,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEG,UAAU,CAACG,MAAM,CAAC;MACpE,CAAC,MAAM,IAAIR,QAAQ,CAACS,IAAI,IAAIT,QAAQ,CAACS,IAAI,CAAC9B,MAAM,IAAI2B,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACS,IAAI,CAAC9B,MAAM,CAAC,EAAE;QACvF0B,UAAU,GAAGL,QAAQ,CAACS,IAAI,CAAC9B,MAAM;QACjCsB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEG,UAAU,CAACG,MAAM,CAAC;MAC3E,CAAC,MAAM,IAAIF,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,EAAE;QAClCK,UAAU,GAAGL,QAAQ;QACrBC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEG,UAAU,CAACG,MAAM,CAAC;MACrE,CAAC,MAAM;QACLP,OAAO,CAACS,IAAI,CAAC,+CAA+C,CAAC;QAC7DL,UAAU,GAAG,EAAE;MACjB;MAEAJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEG,UAAU,CAACM,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;MAE3D;MACA,MAAMC,eAAe,GAAGP,UAAU,CAACQ,GAAG,CAAC1C,IAAI,IAAI;QAC7C,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC5B,OAAOA,IAAI,CAAC2C,WAAW,CAAC,CAAC;QAC3B,CAAC,MAAM,IAAI,OAAO3C,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAAC0B,KAAK,EAAE;UACjD,OAAO1B,IAAI,CAAC0B,KAAK,CAACiB,WAAW,CAAC,CAAC;QACjC,CAAC,MAAM,IAAI,OAAO3C,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAAC2B,KAAK,EAAE;UACjD,OAAO3B,IAAI,CAAC2B,KAAK,CAACgB,WAAW,CAAC,CAAC;QACjC,CAAC,MAAM;UACL,OAAOC,MAAM,CAAC5C,IAAI,CAAC,CAAC2C,WAAW,CAAC,CAAC;QACnC;MACF,CAAC,CAAC,CAACE,MAAM,CAAC7C,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAAC8C,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExChB,OAAO,CAACC,GAAG,CAAC,KAAKU,eAAe,CAACJ,MAAM,qBAAqB,EAAEI,eAAe,CAACD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAC3F/B,SAAS,CAACgC,eAAe,CAAC;IAC5B,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZjB,OAAO,CAAClB,KAAK,CAAC,yBAAyB,EAAEmC,GAAG,CAAC;MAC7C;MACA,MAAMC,cAAc,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;MAC5FlB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEiB,cAAc,CAAC;MACtEvC,SAAS,CAACuC,cAAc,CAAC;IAC3B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAACC,KAAK,EAAExB,KAAK,KAAK;IAC3C7B,UAAU,CAACsD,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACD,KAAK,GAAGxB;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM0B,YAAY,GAAGxF,WAAW,CAAC,OAAOyF,IAAI,GAAG,CAAC,KAAK;IACnD,IAAI;MACF1C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMyC,YAAY,GAAGtB,MAAM,CAACuB,OAAO,CAAC3D,OAAO,CAAC,CAAC4D,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEhC,KAAK,CAAC,KAAK;QACzE,IAAIA,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKiC,SAAS,EAAE;UACzD;UACA,IAAID,GAAG,KAAK,qBAAqB,EAAE;YACjCD,GAAG,CAAC,wBAAwB,CAAC,GAAG/B,KAAK;UACvC,CAAC,MAAM;YACL+B,GAAG,CAACC,GAAG,CAAC,GAAGhC,KAAK;UAClB;QACF;QACA,OAAO+B,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEN;MACAH,YAAY,CAACD,IAAI,GAAGA,IAAI;MAExBvB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuB,YAAY,CAAC;MACvD,MAAMzB,QAAQ,GAAG,MAAMxC,gBAAgB,CAACiE,YAAY,CAAC;MACrDxB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,QAAQ,CAAC;;MAExC;MACA,MAAM+B,YAAY,GAAG/B,QAAQ,CAACS,IAAI,IAAIT,QAAQ;MAE9C,IAAI,CAAC+B,YAAY,CAACC,OAAO,EAAE;QACzB,MAAM,IAAIC,KAAK,CAACF,YAAY,CAAChD,KAAK,IAAI,YAAY,CAAC;MACrD;MAEAL,aAAa,CAACqD,YAAY,CAACtD,UAAU,IAAI,EAAE,CAAC;MAC5CS,aAAa,CAAC6C,YAAY,CAACG,WAAW,IAAI,CAAC,CAAC;MAC5C9C,cAAc,CAACoC,IAAI,CAAC;;MAEpB;MACA,IAAIO,YAAY,CAAC1C,UAAU,EAAE;QAC3BC,aAAa,CAACyC,YAAY,CAAC1C,UAAU,CAAC;MACxC,CAAC,MAAM;QACL;QACA,MAAM8C,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACN,YAAY,CAACG,WAAW,IAAI,CAAC,IAAInE,OAAO,CAACS,KAAK,CAAC;QAC7Ec,aAAa,CAAC;UACZC,YAAY,EAAEiC,IAAI;UAClBhC,WAAW,EAAE2C,UAAU;UACvB1C,SAAS,EAAE1B,OAAO,CAACS,KAAK;UACxBkB,QAAQ,EAAE8B,IAAI,GAAGW,UAAU;UAC3BxC,QAAQ,EAAE6B,IAAI,GAAG;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAON,GAAG,EAAE;MACZjB,OAAO,CAAClB,KAAK,CAAC,iBAAiB,EAAEmC,GAAG,CAAC;MACrClC,QAAQ,CAAC,mCAAmC,GAAGkC,GAAG,CAACoB,OAAO,CAAC;IAC7D,CAAC,SAAS;MACRxD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACf,OAAO,CAAC,CAAC;EAEbjC,SAAS,CAAC,MAAM;IACdiE,UAAU,CAAC,CAAC;IACZ;IACA;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IACzBvE,UAAU,CAAC;MACTC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC,CAAC;IACFY,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMoD,gBAAgB,GAAGA,CAACC,KAAK,EAAEjB,IAAI,KAAK;IACxCvB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsB,IAAI,CAAC;IACrCD,YAAY,CAACC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMkB,kBAAkB,GAAIC,eAAe,IAAK;IAC9C,IAAI,CAACA,eAAe,EAAE;MACpB,OAAO;QACLC,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,SAAS;QAC1BC,SAAS,EAAE,MAAM;QACjBC,IAAI,EAAE;MACR,CAAC;IACH;IAEA,MAAMlD,KAAK,GAAG8C,eAAe,CAAC7B,WAAW,CAAC,CAAC;;IAE3C;IACA,IAAIjB,KAAK,CAACmD,QAAQ,CAAC,WAAW,CAAC,EAAE;MAC/B,OAAO;QACLJ,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MACI,IAAIlD,KAAK,CAACmD,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACnD,KAAK,CAACmD,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACnD,KAAK,CAACmD,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACzF,OAAO;QACLJ,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MACI,IAAIlD,KAAK,CAACmD,QAAQ,CAAC,OAAO,CAAC,EAAE;MAChC,OAAO;QACLJ,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MACI,IAAIlD,KAAK,CAACmD,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACjC,OAAO;QACLJ,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MACI,IAAIlD,KAAK,CAACmD,QAAQ,CAAC,aAAa,CAAC,IAAInD,KAAK,CAACmD,QAAQ,CAAC,MAAM,CAAC,EAAE;MAChE,OAAO;QACLJ,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MACI,IAAIlD,KAAK,CAACmD,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MAC5C,OAAO;QACLJ,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MACI,IAAIlD,KAAK,CAACmD,QAAQ,CAAC,aAAa,CAAC,IAAInD,KAAK,CAACmD,QAAQ,CAAC,OAAO,CAAC,EAAE;MACjE,OAAO;QACLJ,KAAK,EAAE,OAAO;QACdC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MACI,IAAIlD,KAAK,CAACmD,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACjC,OAAO;QACLJ,KAAK,EAAE,OAAO;QACdC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MACI,IAAIlD,KAAK,CAACmD,QAAQ,CAAC,eAAe,CAAC,IAAInD,KAAK,CAACmD,QAAQ,CAAC,YAAY,CAAC,IAAInD,KAAK,CAACmD,QAAQ,CAAC,UAAU,CAAC,EAAE;MACtG,OAAO;QACLJ,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH,CAAC,MACI;MACH,OAAO;QACLH,KAAK,EAAE,SAAS;QAChBC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,SAAS;QACpBC,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAIN,eAAe,IAAK;IAChD,OAAOD,kBAAkB,CAACC,eAAe,CAAC,CAACC,KAAK;EAClD,CAAC;;EAED;EACA,MAAMM,SAAS,GAAGA,CAACrD,KAAK,EAAEsD,YAAY,GAAG,eAAe,KAAK;IAC3D,IAAItD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKiC,SAAS,IAAIjC,KAAK,KAAK,EAAE,EAAE;MACzD,OAAOsD,YAAY;IACrB;IACA,OAAOtD,KAAK;EACd,CAAC;EAED,MAAMuD,UAAU,GAAGA,CAACvD,KAAK,EAAEsD,YAAY,GAAG,CAAC,KAAK;IAC9C,IAAItD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKiC,SAAS,IAAIuB,KAAK,CAACxD,KAAK,CAAC,EAAE;MACzD,OAAOsD,YAAY;IACrB;IACA,OAAOG,MAAM,CAACzD,KAAK,CAAC;EACtB,CAAC;EAED,MAAM0D,YAAY,GAAIpF,IAAI,IAAK;IAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,eAAe;IACjC,OAAOA,IAAI,CAACqF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGtF,IAAI,CAACwC,KAAK,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,oBACE/C,OAAA,CAACtB,GAAG;IAAAoH,QAAA,gBACF9F,OAAA,CAACzB,UAAU;MAACwH,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAAAH,QAAA,gBAClD9F,OAAA,CAACX,UAAU;QAAC6G,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,aAAa,EAAE;QAAS;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,+BAExD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbxG,OAAA,CAACzB,UAAU;MAACwH,OAAO,EAAC,OAAO;MAACf,KAAK,EAAC,gBAAgB;MAACkB,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,EAAC;IAElE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbxG,OAAA,CAAC3B,IAAI;MAAC6H,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,eAClB9F,OAAA,CAAC1B,WAAW;QAAAwH,QAAA,gBACV9F,OAAA,CAACzB,UAAU;UAACwH,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAH,QAAA,gBACnC9F,OAAA,CAACT,UAAU;YAAC2G,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAExD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbxG,OAAA,CAAC5B,IAAI;UAACsI,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAb,QAAA,gBAEzB9F,OAAA,CAAC5B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eAC9B9F,OAAA,CAACxB,SAAS;cACRwI,SAAS;cACT9E,KAAK,EAAC,gBAAgB;cACtB+E,IAAI,EAAC,QAAQ;cACbhF,KAAK,EAAE9B,OAAO,CAACE,SAAU;cACzB6G,QAAQ,EAAGC,CAAC,IAAK3D,kBAAkB,CAAC,WAAW,EAAE2D,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;cACjEoF,UAAU,EAAE;gBAAEC,GAAG,EAAE;cAAE;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPxG,OAAA,CAAC5B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eAC9B9F,OAAA,CAACxB,SAAS;cACRwI,SAAS;cACT9E,KAAK,EAAC,gBAAgB;cACtB+E,IAAI,EAAC,QAAQ;cACbhF,KAAK,EAAE9B,OAAO,CAACG,SAAU;cACzB4G,QAAQ,EAAGC,CAAC,IAAK3D,kBAAkB,CAAC,WAAW,EAAE2D,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;cACjEoF,UAAU,EAAE;gBAAEC,GAAG,EAAE;cAAE;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGPxG,OAAA,CAAC5B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eAC9B9F,OAAA,CAACxB,SAAS;cACR+I,MAAM;cACNrF,KAAK,EAAE,UAAUnB,MAAM,CAAC6B,MAAM,SAAU;cACxCoE,SAAS;cACT/E,KAAK,EAAE9B,OAAO,CAACI,IAAK;cACpB2G,QAAQ,EAAGC,CAAC,IAAK3D,kBAAkB,CAAC,MAAM,EAAE2D,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;cAAA6D,QAAA,gBAE1D9F,OAAA,CAAChB,QAAQ;gBAACiD,KAAK,EAAC,EAAE;gBAAA6D,QAAA,EAAC;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EACjCzF,MAAM,CAACkC,GAAG,CAAC1C,IAAI,iBACdP,OAAA,CAAChB,QAAQ;gBAAYiD,KAAK,EAAE1B,IAAK;gBAAAuF,QAAA,EAC9BvF,IAAI,CAACqF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGtF,IAAI,CAACwC,KAAK,CAAC,CAAC;cAAC,GADhCxC,IAAI;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGPxG,OAAA,CAAC5B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eAC9B9F,OAAA,CAACxB,SAAS;cACR+I,MAAM;cACNrF,KAAK,EAAC,0BAAgB;cACtB8E,SAAS;cACT/E,KAAK,EAAE9B,OAAO,CAACK,SAAU;cACzB0G,QAAQ,EAAGC,CAAC,IAAK3D,kBAAkB,CAAC,WAAW,EAAE2D,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;cAAA6D,QAAA,gBAE/D9F,OAAA,CAAChB,QAAQ;gBAACiD,KAAK,EAAC,EAAE;gBAAA6D,QAAA,EAAC;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EACjC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACvD,GAAG,CAACuE,GAAG,iBACzBxH,OAAA,CAAChB,QAAQ;gBAAWiD,KAAK,EAAEuF,GAAI;gBAAA1B,QAAA,EAAE0B;cAAG,GAArBA,GAAG;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEPxG,OAAA,CAAC5B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eAC9B9F,OAAA,CAACxB,SAAS;cACR+I,MAAM;cACNrF,KAAK,EAAC,0BAAgB;cACtB8E,SAAS;cACT/E,KAAK,EAAE9B,OAAO,CAACM,SAAU;cACzByG,QAAQ,EAAGC,CAAC,IAAK3D,kBAAkB,CAAC,WAAW,EAAE2D,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;cAAA6D,QAAA,gBAE/D9F,OAAA,CAAChB,QAAQ;gBAACiD,KAAK,EAAC,EAAE;gBAAA6D,QAAA,EAAC;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EACjC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACvD,GAAG,CAACuE,GAAG,iBACzBxH,OAAA,CAAChB,QAAQ;gBAAWiD,KAAK,EAAEuF,GAAI;gBAAA1B,QAAA,EAAE0B;cAAG,GAArBA,GAAG;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGPxG,OAAA,CAAC5B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eAC9B9F,OAAA,CAACxB,SAAS;cACRwI,SAAS;cACT9E,KAAK,EAAC,eAAe;cACrB+E,IAAI,EAAC,QAAQ;cACbhF,KAAK,EAAE9B,OAAO,CAACO,QAAS;cACxBwG,QAAQ,EAAGC,CAAC,IAAK3D,kBAAkB,CAAC,UAAU,EAAE2D,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;cAChEoF,UAAU,EAAE;gBAAEC,GAAG,EAAE;cAAE;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPxG,OAAA,CAAC5B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eAC9B9F,OAAA,CAACxB,SAAS;cACRwI,SAAS;cACT9E,KAAK,EAAC,eAAe;cACrB+E,IAAI,EAAC,QAAQ;cACbhF,KAAK,EAAE9B,OAAO,CAACQ,QAAS;cACxBuG,QAAQ,EAAGC,CAAC,IAAK3D,kBAAkB,CAAC,UAAU,EAAE2D,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;cAChEoF,UAAU,EAAE;gBAAEC,GAAG,EAAE;cAAE;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPxG,OAAA,CAAC5B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjB,QAAA,eAC9B9F,OAAA,CAACxB,SAAS;cACR+I,MAAM;cACNrF,KAAK,EAAC,2BAAc;cACpB8E,SAAS;cACT/E,KAAK,EAAE9B,OAAO,CAACS,KAAM;cACrBsG,QAAQ,EAAGC,CAAC,IAAK3D,kBAAkB,CAAC,OAAO,EAAE2D,CAAC,CAACC,MAAM,CAACnF,KAAK,CAAE;cAAA6D,QAAA,gBAE3D9F,OAAA,CAAChB,QAAQ;gBAACiD,KAAK,EAAE,EAAG;gBAAA6D,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCxG,OAAA,CAAChB,QAAQ;gBAACiD,KAAK,EAAE,EAAG;gBAAA6D,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCxG,OAAA,CAAChB,QAAQ;gBAACiD,KAAK,EAAE,EAAG;gBAAA6D,QAAA,EAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCxG,OAAA,CAAChB,QAAQ;gBAACiD,KAAK,EAAE,GAAI;gBAAA6D,QAAA,EAAC;cAAG;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPxG,OAAA,CAACtB,GAAG;UAACwH,EAAE,EAAE;YAAEuB,EAAE,EAAE,CAAC;YAAEC,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAA7B,QAAA,gBAC1C9F,OAAA,CAACvB,MAAM;YACLsH,OAAO,EAAC,WAAW;YACnB6B,OAAO,EAAEA,CAAA,KAAMjE,YAAY,CAAC,CAAC,CAAE;YAC/BkE,QAAQ,EAAE5G,OAAQ;YAClB6G,SAAS,EAAE7G,OAAO,gBAAGjB,OAAA,CAACpB,gBAAgB;cAACmJ,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxG,OAAA,CAACX,UAAU;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAV,QAAA,EAEpE7E,OAAO,GAAG,aAAa,GAAG;UAAK;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAETxG,OAAA,CAACvB,MAAM;YACLsH,OAAO,EAAC,UAAU;YAClB6B,OAAO,EAAEjD,YAAa;YACtBkD,QAAQ,EAAE5G,OAAQ;YAAA6E,QAAA,EACnB;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGNrF,KAAK,iBACJnB,OAAA,CAACrB,KAAK;MAACqJ,QAAQ,EAAC,OAAO;MAAC9B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,EACnC3E;IAAK;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGA,CAACvF,OAAO,IAAIJ,UAAU,CAAC+B,MAAM,GAAG,CAAC,iBAChC5C,OAAA,CAACtB,GAAG;MAACwH,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,eACjB9F,OAAA,CAACzB,UAAU;QAACwH,OAAO,EAAC,IAAI;QAAAD,QAAA,GACrBzE,UAAU,EAAC,eACd;MAAA;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGDxG,OAAA,CAAC5B,IAAI;MAACsI,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAb,QAAA,EACxBjF,UAAU,CAACoC,GAAG,CAAC,CAACgF,QAAQ,EAAEC,KAAK,KAAK;QACnC;QACA,MAAMC,kBAAkB,GAAG;UACzBC,EAAE,EAAEH,QAAQ,CAACG,EAAE,IAAIF,KAAK;UACxBG,KAAK,EAAEJ,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACI,KAAK,IAAI,CAAC;UAC5CE,aAAa,EAAEN,QAAQ,CAACO,aAAa,IAAIP,QAAQ,CAACM,aAAa,IAAI,CAAC;UACpEE,UAAU,EAAER,QAAQ,CAACS,UAAU,IAAIT,QAAQ,CAACQ,UAAU,IAAIR,QAAQ,CAACU,UAAU,IAAI,CAAC;UAClFC,KAAK,EAAEX,QAAQ,CAACY,KAAK,IAAIZ,QAAQ,CAACW,KAAK,IAAIX,QAAQ,CAAC1H,IAAI,IAAI,eAAe;UAC3EuI,aAAa,EAAEb,QAAQ,CAACc,aAAa,IAAId,QAAQ,CAACa,aAAa,IAAIb,QAAQ,CAACe,aAAa,IAAI,eAAe;UAC5GC,YAAY,EAAEhB,QAAQ,CAACiB,YAAY,IAAIjB,QAAQ,CAACgB,YAAY,IAAIhB,QAAQ,CAACkB,YAAY,IAAI,eAAe;UACxGC,WAAW,EAAEnB,QAAQ,CAACoB,WAAW,IAAIpB,QAAQ,CAACmB,WAAW,IAAInB,QAAQ,CAACqB,WAAW,IAAI,eAAe;UACpGC,WAAW,EAAEtB,QAAQ,CAACuB,WAAW,IAAIvB,QAAQ,CAACsB,WAAW,IAAItB,QAAQ,CAACwB,WAAW,IAAI,eAAe;UACpGC,gBAAgB,EAAEzB,QAAQ,CAAC0B,iBAAiB,IAAI1B,QAAQ,CAACyB,gBAAgB,IAAIzB,QAAQ,CAAC2B,gBAAgB,IAAI,eAAe;UACzHC,YAAY,EAAE5B,QAAQ,CAAC6B,YAAY,IAAI7B,QAAQ,CAAC4B,YAAY,IAAI5B,QAAQ,CAAC8B,YAAY,IAAI,CAAC;UAC1FC,aAAa,EAAE/B,QAAQ,CAAC+B,aAAa,KAAK/B,QAAQ,CAACI,KAAK,IAAIJ,QAAQ,CAACM,aAAa,GAAGN,QAAQ,CAACI,KAAK,GAAGJ,QAAQ,CAACM,aAAa,GAAG,CAAC;QAClI,CAAC;QAEDlG,OAAO,CAACC,GAAG,CAAC,WAAW4F,KAAK,GAAG,CAAC,oBAAoB,EAAEC,kBAAkB,CAAC;QAEzE,oBACEnI,OAAA,CAAC5B,IAAI;UAACwI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAACkD,EAAE,EAAE,CAAE;UAAAnE,QAAA,eACrC9F,OAAA,CAAC3B,IAAI;YAAC6H,EAAE,EAAE;cAAEgE,MAAM,EAAE,MAAM;cAAExC,OAAO,EAAE,MAAM;cAAEyC,aAAa,EAAE;YAAS,CAAE;YAAArE,QAAA,eACrE9F,OAAA,CAAC1B,WAAW;cAAC4H,EAAE,EAAE;gBAAEkE,QAAQ,EAAE;cAAE,CAAE;cAAAtE,QAAA,gBAE/B9F,OAAA,CAACzB,UAAU;gBAACwH,OAAO,EAAC,IAAI;gBAACf,KAAK,EAAC,SAAS;gBAACiB,YAAY;gBAAAH,QAAA,EAClDhG,WAAW,CAAC0F,UAAU,CAAC2C,kBAAkB,CAACE,KAAK,CAAC;cAAC;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,EAGZ2B,kBAAkB,CAAC6B,aAAa,GAAG,CAAC,iBACnChK,OAAA,CAACzB,UAAU;gBAACwH,OAAO,EAAC,SAAS;gBAACf,KAAK,EAAC,gBAAgB;gBAAC0C,OAAO,EAAC,OAAO;gBAACzB,YAAY;gBAAAH,QAAA,GAC9EhG,WAAW,CAACqI,kBAAkB,CAAC6B,aAAa,CAAC,EAAC,QACjD;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb,eAGDxG,OAAA,CAACtB,GAAG;gBAACwH,EAAE,EAAE;kBAAEO,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,gBACjB9F,OAAA,CAACzB,UAAU;kBAACwH,OAAO,EAAC,OAAO;kBAACE,YAAY;kBAAAH,QAAA,gBACtC9F,OAAA,CAACP,QAAQ;oBAACyG,EAAE,EAAE;sBAAEmE,QAAQ,EAAE,EAAE;sBAAElE,EAAE,EAAE,GAAG;sBAAEC,aAAa,EAAE;oBAAS;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnEhB,UAAU,CAAC2C,kBAAkB,CAACI,aAAa,CAAC,EAAC,gBAAM,EAAC/C,UAAU,CAAC2C,kBAAkB,CAACM,UAAU,CAAC,EAAC,MACjG;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAEbxG,OAAA,CAACzB,UAAU;kBAACwH,OAAO,EAAC,OAAO;kBAACf,KAAK,EAAC,gBAAgB;kBAACiB,YAAY;kBAAAH,QAAA,gBAC7D9F,OAAA,CAACL,YAAY;oBAACuG,EAAE,EAAE;sBAAEmE,QAAQ,EAAE,EAAE;sBAAElE,EAAE,EAAE,GAAG;sBAAEC,aAAa,EAAE;oBAAS;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACvEb,YAAY,CAACwC,kBAAkB,CAACS,KAAK,CAAC;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENxG,OAAA,CAACb,OAAO;gBAAC+G,EAAE,EAAE;kBAAEoE,EAAE,EAAE;gBAAE;cAAE;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAG1BxG,OAAA,CAACtB,GAAG;gBAACwH,EAAE,EAAE;kBAAEO,EAAE,EAAE;gBAAE,CAAE;gBAAAX,QAAA,gBACjB9F,OAAA,CAACzB,UAAU;kBAACwH,OAAO,EAAC,SAAS;kBAAC2B,OAAO,EAAC,OAAO;kBAAC1C,KAAK,EAAC,gBAAgB;kBAAAc,QAAA,GAAC,OAC9D,EAACR,SAAS,CAAC6C,kBAAkB,CAACW,aAAa,CAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACbxG,OAAA,CAACzB,UAAU;kBAACwH,OAAO,EAAC,SAAS;kBAAC2B,OAAO,EAAC,OAAO;kBAAC1C,KAAK,EAAC,gBAAgB;kBAAAc,QAAA,GAAC,uBACxD,EAACR,SAAS,CAAC6C,kBAAkB,CAACc,YAAY,CAAC;gBAAA;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACbxG,OAAA,CAACzB,UAAU;kBAACwH,OAAO,EAAC,SAAS;kBAAC2B,OAAO,EAAC,OAAO;kBAAC1C,KAAK,EAAC,gBAAgB;kBAAAc,QAAA,GAAC,eAC3D,EAACR,SAAS,CAAC6C,kBAAkB,CAACiB,WAAW,CAAC;gBAAA;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACbxG,OAAA,CAACzB,UAAU;kBAACwH,OAAO,EAAC,SAAS;kBAAC2B,OAAO,EAAC,OAAO;kBAAC1C,KAAK,EAAC,gBAAgB;kBAAAc,QAAA,GAAC,aAC7D,EAACR,SAAS,CAAC6C,kBAAkB,CAACoB,WAAW,CAAC;gBAAA;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,EACZ2B,kBAAkB,CAAC0B,YAAY,GAAG,CAAC,iBAClC7J,OAAA,CAACzB,UAAU;kBAACwH,OAAO,EAAC,SAAS;kBAAC2B,OAAO,EAAC,OAAO;kBAAC1C,KAAK,EAAC,gBAAgB;kBAAAc,QAAA,GAAC,SAC5D,EAACqC,kBAAkB,CAAC0B,YAAY;gBAAA;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNxG,OAAA,CAACtB,GAAG;gBAACwH,EAAE,EAAE;kBAAEuB,EAAE,EAAE;gBAAO,CAAE;gBAAA3B,QAAA,EACrB,CAAC,MAAM;kBACN,MAAMyE,eAAe,GAAGzF,kBAAkB,CAACqD,kBAAkB,CAACuB,gBAAgB,CAAC;kBAC/E,MAAMc,eAAe,GAAGlF,SAAS,CAAC6C,kBAAkB,CAACuB,gBAAgB,EAAE,eAAe,CAAC;kBAEvF,oBACE1J,OAAA,CAACf,IAAI;oBACHiD,KAAK,EAAE,GAAGqI,eAAe,CAACpF,IAAI,IAAIqF,eAAe,EAAG;oBACpDtE,EAAE,EAAE;sBACFjB,eAAe,EAAEsF,eAAe,CAACtF,eAAe;sBAChDD,KAAK,EAAEuF,eAAe,CAACrF,SAAS;sBAChCuF,UAAU,EAAE,MAAM;sBAClBJ,QAAQ,EAAE,SAAS;sBACnB,kBAAkB,EAAE;wBAClBrF,KAAK,EAAEuF,eAAe,CAACrF;sBACzB;oBACF,CAAE;oBACF6C,IAAI,EAAC;kBAAO;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAEN,CAAC,EAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA3EoC2B,kBAAkB,CAACC,EAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4E5D,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGN,CAACvF,OAAO,IAAIJ,UAAU,CAAC+B,MAAM,KAAK,CAAC,iBAClC5C,OAAA,CAAC3B,IAAI;MAAAyH,QAAA,eACH9F,OAAA,CAAC1B,WAAW;QAAC4H,EAAE,EAAE;UAAEwE,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA7E,QAAA,gBAC9C9F,OAAA,CAACX,UAAU;UAAC6G,EAAE,EAAE;YAAEmE,QAAQ,EAAE,EAAE;YAAErF,KAAK,EAAE,UAAU;YAAEyB,EAAE,EAAE;UAAE;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DxG,OAAA,CAACzB,UAAU;UAACwH,OAAO,EAAC,IAAI;UAACf,KAAK,EAAC,gBAAgB;UAAAc,QAAA,EAAC;QAEhD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxG,OAAA,CAACzB,UAAU;UAACwH,OAAO,EAAC,OAAO;UAACf,KAAK,EAAC,gBAAgB;UAAAc,QAAA,EAAC;QAEnD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,EAGA,CAACvF,OAAO,IAAIJ,UAAU,CAAC+B,MAAM,GAAG,CAAC,IAAInB,UAAU,CAACG,WAAW,GAAG,CAAC,iBAC9D5B,OAAA,CAACtB,GAAG;MAACwH,EAAE,EAAE;QAAEwB,OAAO,EAAE,MAAM;QAAEkD,cAAc,EAAE,QAAQ;QAAEnD,EAAE,EAAE,CAAC;QAAEhB,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,eACnE9F,OAAA,CAACtB,GAAG;QAACwH,EAAE,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEyC,aAAa,EAAE,QAAQ;UAAEU,UAAU,EAAE,QAAQ;UAAElD,GAAG,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBAClF9F,OAAA,CAACzB,UAAU;UAACwH,OAAO,EAAC,OAAO;UAACf,KAAK,EAAC,gBAAgB;UAAAc,QAAA,GAAC,QAC3C,EAACrE,UAAU,CAACE,YAAY,EAAC,KAAG,EAACF,UAAU,CAACG,WAAW,EAAC,GACzD,EAACP,UAAU,EAAC,eAAU,EAAE,CAACI,UAAU,CAACE,YAAY,GAAG,CAAC,IAAIF,UAAU,CAACI,SAAS,GAAI,CAAC,EAAC,GAAC,EAAC2C,IAAI,CAAC8C,GAAG,CAAC7F,UAAU,CAACE,YAAY,GAAGF,UAAU,CAACI,SAAS,EAAER,UAAU,CAAC,EAAC,8BAC5J;QAAA;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxG,OAAA,CAACd,UAAU;UACT4L,KAAK,EAAErJ,UAAU,CAACG,WAAY;UAC9BgC,IAAI,EAAEnC,UAAU,CAACE,YAAa;UAC9BuF,QAAQ,EAAEtC,gBAAiB;UAC3BI,KAAK,EAAC,SAAS;UACf+C,IAAI,EAAC,OAAO;UACZgD,eAAe;UACfC,cAAc;UACdC,YAAY,EAAE,CAAE;UAChBC,aAAa,EAAE;QAAE;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtG,EAAA,CAxmBID,cAAc;AAAAkL,EAAA,GAAdlL,cAAc;AA0mBpB,eAAeA,cAAc;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}