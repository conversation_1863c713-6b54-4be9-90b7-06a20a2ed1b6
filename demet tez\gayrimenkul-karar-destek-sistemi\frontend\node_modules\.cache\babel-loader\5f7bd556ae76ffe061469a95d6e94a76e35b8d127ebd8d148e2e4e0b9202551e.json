{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demet tez\\\\demet tez\\\\gayrimenkul-karar-destek-sistemi\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Grid, Card, CardContent, Typography, Button, Box, Chip } from '@mui/material';\nimport { TrendingUp as PredictionIcon, Analytics as AnalyticsIcon, Search as SearchIcon, Home as HomeIcon, Assessment as InvestmentIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  var _marketData$general_s, _marketData$general_s2, _marketData$general_s3, _marketData$general_s4, _marketData$general_s5;\n  const navigate = useNavigate();\n  const features = [{\n    title: '<PERSON><PERSON><PERSON>',\n    description: 'Gelişmiş ML algoritmaları ile doğru fiyat tahmini. Random Forest, XGBoost ve Stacking Ensemble modelleri kullanılır.',\n    icon: /*#__PURE__*/_jsxDEV(PredictionIcon, {\n      sx: {\n        fontSize: 40,\n        color: 'primary.main'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this),\n    action: () => navigate('/prediction'),\n    buttonText: 'Tahmin Yap',\n    stats: '95% Doğruluk'\n  }, {\n    title: 'Piyasa Analizi',\n    description: 'Şehir bazında detaylı piyasa analizleri, fiyat trendleri ve yatırım fırsatlarını keşfedin.',\n    icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n      sx: {\n        fontSize: 40,\n        color: 'secondary.main'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    action: () => navigate('/analysis'),\n    buttonText: 'Analiz Et',\n    stats: '50+ Şehir'\n  }, {\n    title: 'İlan Arama',\n    description: 'Akıllı filtreleme ve yatırım uygunluk skorları ile 19,000+ ilan arasından ideal gayrimenkulü bulun.',\n    icon: /*#__PURE__*/_jsxDEV(SearchIcon, {\n      sx: {\n        fontSize: 40,\n        color: 'success.main'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this),\n    action: () => navigate('/properties'),\n    buttonText: 'İlan Ara',\n    stats: '19,000+ İlan'\n  }, {\n    title: 'Yatırım Analizi',\n    description: 'Gayrimenkul yatırım uygunluğunu değerlendirin. Çok Uygun, Makul Fiyat, Uygun Değil gibi detaylı kategoriler.',\n    icon: /*#__PURE__*/_jsxDEV(InvestmentIcon, {\n      sx: {\n        fontSize: 40,\n        color: 'warning.main'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this),\n    action: () => navigate('/properties'),\n    buttonText: 'Yatırım Analizi',\n    stats: '9 Kategori'\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4,\n        background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',\n        color: 'white'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          py: 6\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 8,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              component: \"h1\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              children: \"Gayrimenkul Karar Destek Sistemi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 3,\n                opacity: 0.9\n              },\n              children: \"Veri madencili\\u011Fi ve makine \\xF6\\u011Frenmesi teknikleri ile gayrimenkul de\\u011Ferlendirmesi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 4,\n                opacity: 0.8\n              },\n              children: \"Bu sistem, geli\\u015Fmi\\u015F makine \\xF6\\u011Frenmesi algoritmalar\\u0131 kullanarak gayrimenkul fiyat tahmini, piyasa analizi ve ak\\u0131ll\\u0131 yat\\u0131r\\u0131m \\xF6nerileri sunar. Veri madencili\\u011Fi teknikleri ile en do\\u011Fru sonu\\xE7lar\\u0131 elde edin.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Random Forest\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"XGBoost\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Stacking Ensemble\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Yat\\u0131r\\u0131m Analizi\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Veri Madencili\\u011Fi\",\n                variant: \"outlined\",\n                sx: {\n                  color: 'white',\n                  borderColor: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            textAlign: \"center\",\n            children: /*#__PURE__*/_jsxDEV(HomeIcon, {\n              sx: {\n                fontSize: 120,\n                opacity: 0.3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"warning\",\n      sx: {\n        mb: 3\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this), stats && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Sistem Durumu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: stats.backend_status === 'up' ? '✓' : '✗'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Backend API\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: stats.ml_api_status === 'up' ? '✓' : '✗'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"ML Servisi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: [Math.round(stats.uptime / 60), \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"\\xC7al\\u0131\\u015Fma S\\xFCresi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                color: \"primary\",\n                children: stats.node_version\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Node.js\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h2\",\n      gutterBottom: true,\n      textAlign: \"center\",\n      sx: {\n        mb: 4\n      },\n      children: \"Sistem \\xD6zellikleri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      sx: {\n        mb: 4\n      },\n      children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        lg: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            minHeight: '300px' // Minimum yükseklik belirle\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1,\n              textAlign: 'center',\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'space-between' // İçeriği eşit dağıt\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 2\n                },\n                children: feature.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                component: \"h3\",\n                gutterBottom: true,\n                children: feature.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mb: 2\n                },\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), feature.stats && /*#__PURE__*/_jsxDEV(Chip, {\n                label: feature.stats,\n                color: \"primary\",\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  mb: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              onClick: feature.action,\n              fullWidth: true,\n              size: \"large\",\n              sx: {\n                mt: 2\n              } // Üstten sabit mesafe\n              ,\n              children: feature.buttonText\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), marketData && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\uD83D\\uDCCA Piyasa \\xD6zeti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"G\\xFCncel piyasa verileri ve istatistikler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Toplam \\u0130lan Say\\u0131s\\u0131\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: ((_marketData$general_s = marketData.general_stats) === null || _marketData$general_s === void 0 ? void 0 : (_marketData$general_s2 = _marketData$general_s.total_properties) === null || _marketData$general_s2 === void 0 ? void 0 : _marketData$general_s2.toLocaleString('tr-TR')) || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Ortalama Fiyat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: (_marketData$general_s3 = marketData.general_stats) !== null && _marketData$general_s3 !== void 0 && _marketData$general_s3.avg_price ? `₺${Math.round(marketData.general_stats.avg_price).toLocaleString('tr-TR')}` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Ortalama Metrekare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: (_marketData$general_s4 = marketData.general_stats) !== null && _marketData$general_s4 !== void 0 && _marketData$general_s4.avg_area ? `${Math.round(marketData.general_stats.avg_area)} m²` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: \"Ortalama Oda Say\\u0131s\\u0131\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: (_marketData$general_s5 = marketData.general_stats) !== null && _marketData$general_s5 !== void 0 && _marketData$general_s5.avg_rooms ? marketData.general_stats.avg_rooms.toFixed(1) : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Box", "Chip", "TrendingUp", "PredictionIcon", "Analytics", "AnalyticsIcon", "Search", "SearchIcon", "Home", "HomeIcon", "Assessment", "InvestmentIcon", "useNavigate", "jsxDEV", "_jsxDEV", "HomePage", "_s", "_marketData$general_s", "_marketData$general_s2", "_marketData$general_s3", "_marketData$general_s4", "_marketData$general_s5", "navigate", "features", "title", "description", "icon", "sx", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "action", "buttonText", "stats", "loading", "display", "justifyContent", "alignItems", "minHeight", "children", "CircularProgress", "size", "mb", "background", "py", "container", "spacing", "item", "xs", "md", "variant", "component", "gutterBottom", "fontWeight", "opacity", "gap", "flexWrap", "label", "borderColor", "textAlign", "error", "<PERSON><PERSON>", "severity", "backend_status", "ml_api_status", "Math", "round", "uptime", "node_version", "map", "feature", "index", "sm", "lg", "height", "flexDirection", "flexGrow", "onClick", "fullWidth", "mt", "marketData", "Divider", "general_stats", "total_properties", "toLocaleString", "avg_price", "avg_area", "avg_rooms", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/demet tez/demet tez/gayrimenkul-karar-destek-sistemi/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON>,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Button,\n  Box,\n  Chip\n} from '@mui/material';\nimport {\n  TrendingUp as PredictionIcon,\n  Analytics as AnalyticsIcon,\n  Search as SearchIcon,\n  Home as HomeIcon,\n  Assessment as InvestmentIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\n\nconst HomePage = () => {\n  const navigate = useNavigate();\n\n\n\n  const features = [\n    {\n      title: 'Fiyat Tahmini',\n      description: 'Gelişmiş ML algoritmaları ile doğru fiyat tahmini. Random Forest, XGBoost ve Stacking Ensemble modelleri kullanılır.',\n      icon: <PredictionIcon sx={{ fontSize: 40, color: 'primary.main' }} />,\n      action: () => navigate('/prediction'),\n      buttonText: '<PERSON><PERSON><PERSON> Yap',\n      stats: '95% Doğruluk'\n    },\n    {\n      title: 'Piyasa Analizi',\n      description: 'Şehir bazında detaylı piyasa analizleri, fiyat trendleri ve yatırım fırsatlarını keşfedin.',\n      icon: <AnalyticsIcon sx={{ fontSize: 40, color: 'secondary.main' }} />,\n      action: () => navigate('/analysis'),\n      buttonText: 'Analiz Et',\n      stats: '50+ Şehir'\n    },\n    {\n      title: 'İlan Arama',\n      description: 'Akıllı filtreleme ve yatırım uygunluk skorları ile 19,000+ ilan arasından ideal gayrimenkulü bulun.',\n      icon: <SearchIcon sx={{ fontSize: 40, color: 'success.main' }} />,\n      action: () => navigate('/properties'),\n      buttonText: 'İlan Ara',\n      stats: '19,000+ İlan'\n    },\n    {\n      title: 'Yatırım Analizi',\n      description: 'Gayrimenkul yatırım uygunluğunu değerlendirin. Çok Uygun, Makul Fiyat, Uygun Değil gibi detaylı kategoriler.',\n      icon: <InvestmentIcon sx={{ fontSize: 40, color: 'warning.main' }} />,\n      action: () => navigate('/properties'),\n      buttonText: 'Yatırım Analizi',\n      stats: '9 Kategori'\n    }\n  ];\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Hero Section */}\n      <Card sx={{ mb: 4, background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)', color: 'white' }}>\n        <CardContent sx={{ py: 6 }}>\n          <Grid container spacing={4} alignItems=\"center\">\n            <Grid item xs={12} md={8}>\n              <Typography variant=\"h3\" component=\"h1\" gutterBottom fontWeight=\"bold\">\n                Gayrimenkul Karar Destek Sistemi\n              </Typography>\n              <Typography variant=\"h6\" sx={{ mb: 3, opacity: 0.9 }}>\n                Veri madenciliği ve makine öğrenmesi teknikleri ile gayrimenkul değerlendirmesi\n              </Typography>\n              <Typography variant=\"body1\" sx={{ mb: 4, opacity: 0.8 }}>\n                Bu sistem, gelişmiş makine öğrenmesi algoritmaları kullanarak gayrimenkul fiyat tahmini,\n                piyasa analizi ve akıllı yatırım önerileri sunar. Veri madenciliği teknikleri ile\n                en doğru sonuçları elde edin.\n              </Typography>\n              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n                <Chip label=\"Random Forest\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n                <Chip label=\"XGBoost\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n                <Chip label=\"Stacking Ensemble\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n                <Chip label=\"Yatırım Analizi\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n                <Chip label=\"Veri Madenciliği\" variant=\"outlined\" sx={{ color: 'white', borderColor: 'white' }} />\n              </Box>\n            </Grid>\n            <Grid item xs={12} md={4} textAlign=\"center\">\n              <HomeIcon sx={{ fontSize: 120, opacity: 0.3 }} />\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* System Status */}\n      {error && (\n        <Alert severity=\"warning\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      {stats && (\n        <Card sx={{ mb: 4 }}>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Sistem Durumu\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={6} md={3}>\n                <Box textAlign=\"center\">\n                  <Typography variant=\"h4\" color=\"primary\">\n                    {stats.backend_status === 'up' ? '✓' : '✗'}\n                  </Typography>\n                  <Typography variant=\"body2\">Backend API</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={6} md={3}>\n                <Box textAlign=\"center\">\n                  <Typography variant=\"h4\" color=\"primary\">\n                    {stats.ml_api_status === 'up' ? '✓' : '✗'}\n                  </Typography>\n                  <Typography variant=\"body2\">ML Servisi</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={6} md={3}>\n                <Box textAlign=\"center\">\n                  <Typography variant=\"h4\" color=\"primary\">\n                    {Math.round(stats.uptime / 60)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Çalışma Süresi</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={6} md={3}>\n                <Box textAlign=\"center\">\n                  <Typography variant=\"h4\" color=\"primary\">\n                    {stats.node_version}\n                  </Typography>\n                  <Typography variant=\"body2\">Node.js</Typography>\n                </Box>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Features */}\n      <Typography variant=\"h4\" component=\"h2\" gutterBottom textAlign=\"center\" sx={{ mb: 4 }}>\n        Sistem Özellikleri\n      </Typography>\n\n      <Grid container spacing={4} sx={{ mb: 4 }}>\n        {features.map((feature, index) => (\n          <Grid item xs={12} sm={6} lg={3} key={index}>\n            <Card sx={{\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column',\n              minHeight: '300px' // Minimum yükseklik belirle\n            }}>\n              <CardContent sx={{\n                flexGrow: 1,\n                textAlign: 'center',\n                display: 'flex',\n                flexDirection: 'column',\n                justifyContent: 'space-between' // İçeriği eşit dağıt\n              }}>\n                <Box>\n                  <Box sx={{ mb: 2 }}>\n                    {feature.icon}\n                  </Box>\n                  <Typography variant=\"h5\" component=\"h3\" gutterBottom>\n                    {feature.title}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    {feature.description}\n                  </Typography>\n                  {feature.stats && (\n                    <Chip\n                      label={feature.stats}\n                      color=\"primary\"\n                      variant=\"outlined\"\n                      size=\"small\"\n                      sx={{ mb: 2 }}\n                    />\n                  )}\n                </Box>\n                <Button\n                  variant=\"contained\"\n                  onClick={feature.action}\n                  fullWidth\n                  size=\"large\"\n                  sx={{ mt: 2 }} // Üstten sabit mesafe\n                >\n                  {feature.buttonText}\n                </Button>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Market Overview */}\n      {marketData && (\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              📊 Piyasa Özeti\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              Güncel piyasa verileri ve istatistikler\n            </Typography>\n            <Divider sx={{ mb: 3 }} />\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Toplam İlan Sayısı\n                </Typography>\n                <Typography variant=\"h4\" color=\"primary\">\n                  {marketData.general_stats?.total_properties?.toLocaleString('tr-TR') || 'N/A'}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Ortalama Fiyat\n                </Typography>\n                <Typography variant=\"h4\" color=\"primary\">\n                  {marketData.general_stats?.avg_price ?\n                    `₺${Math.round(marketData.general_stats.avg_price).toLocaleString('tr-TR')}` :\n                    'N/A'\n                  }\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Ortalama Metrekare\n                </Typography>\n                <Typography variant=\"h4\" color=\"primary\">\n                  {marketData.general_stats?.avg_area ?\n                    `${Math.round(marketData.general_stats.avg_area)} m²` :\n                    'N/A'\n                  }\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Ortalama Oda Sayısı\n                </Typography>\n                <Typography variant=\"h4\" color=\"primary\">\n                  {marketData.general_stats?.avg_rooms ?\n                    marketData.general_stats.avg_rooms.toFixed(1) :\n                    'N/A'\n                  }\n                </Typography>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,GAAG,EACHC,IAAI,QACC,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACrB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAI9B,MAAMW,QAAQ,GAAG,CACf;IACEC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,sHAAsH;IACnIC,IAAI,eAAEZ,OAAA,CAACX,cAAc;MAACwB,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAe;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrEC,MAAM,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,aAAa,CAAC;IACrCa,UAAU,EAAE,YAAY;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEZ,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,4FAA4F;IACzGC,IAAI,eAAEZ,OAAA,CAACT,aAAa;MAACsB,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAiB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtEC,MAAM,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,WAAW,CAAC;IACnCa,UAAU,EAAE,WAAW;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEZ,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,qGAAqG;IAClHC,IAAI,eAAEZ,OAAA,CAACP,UAAU;MAACoB,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAe;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjEC,MAAM,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,aAAa,CAAC;IACrCa,UAAU,EAAE,UAAU;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEZ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,8GAA8G;IAC3HC,IAAI,eAAEZ,OAAA,CAACH,cAAc;MAACgB,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAe;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrEC,MAAM,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,aAAa,CAAC;IACrCa,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC,CACF;EAED,IAAIC,OAAO,EAAE;IACX,oBACEvB,OAAA,CAACd,GAAG;MAACsC,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E5B,OAAA,CAAC6B,gBAAgB;QAACC,IAAI,EAAE;MAAG;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,oBACEnB,OAAA,CAACd,GAAG;IAAA0C,QAAA,gBAEF5B,OAAA,CAAClB,IAAI;MAAC+B,EAAE,EAAE;QAAEkB,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE,mDAAmD;QAAEjB,KAAK,EAAE;MAAQ,CAAE;MAAAa,QAAA,eACnG5B,OAAA,CAACjB,WAAW;QAAC8B,EAAE,EAAE;UAAEoB,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,eACzB5B,OAAA,CAACnB,IAAI;UAACqD,SAAS;UAACC,OAAO,EAAE,CAAE;UAACT,UAAU,EAAC,QAAQ;UAAAE,QAAA,gBAC7C5B,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvB5B,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,IAAI;cAACC,YAAY;cAACC,UAAU,EAAC,MAAM;cAAAd,QAAA,EAAC;YAEvE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,IAAI;cAAC1B,EAAE,EAAE;gBAAEkB,EAAE,EAAE,CAAC;gBAAEY,OAAO,EAAE;cAAI,CAAE;cAAAf,QAAA,EAAC;YAEtD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,OAAO;cAAC1B,EAAE,EAAE;gBAAEkB,EAAE,EAAE,CAAC;gBAAEY,OAAO,EAAE;cAAI,CAAE;cAAAf,QAAA,EAAC;YAIzD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAACd,GAAG;cAAC2B,EAAE,EAAE;gBAAEW,OAAO,EAAE,MAAM;gBAAEoB,GAAG,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAjB,QAAA,gBACrD5B,OAAA,CAACb,IAAI;gBAAC2D,KAAK,EAAC,eAAe;gBAACP,OAAO,EAAC,UAAU;gBAAC1B,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAEgC,WAAW,EAAE;gBAAQ;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/FnB,OAAA,CAACb,IAAI;gBAAC2D,KAAK,EAAC,SAAS;gBAACP,OAAO,EAAC,UAAU;gBAAC1B,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAEgC,WAAW,EAAE;gBAAQ;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzFnB,OAAA,CAACb,IAAI;gBAAC2D,KAAK,EAAC,mBAAmB;gBAACP,OAAO,EAAC,UAAU;gBAAC1B,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAEgC,WAAW,EAAE;gBAAQ;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnGnB,OAAA,CAACb,IAAI;gBAAC2D,KAAK,EAAC,2BAAiB;gBAACP,OAAO,EAAC,UAAU;gBAAC1B,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAEgC,WAAW,EAAE;gBAAQ;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjGnB,OAAA,CAACb,IAAI;gBAAC2D,KAAK,EAAC,uBAAkB;gBAACP,OAAO,EAAC,UAAU;gBAAC1B,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAEgC,WAAW,EAAE;gBAAQ;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACU,SAAS,EAAC,QAAQ;YAAApB,QAAA,eAC1C5B,OAAA,CAACL,QAAQ;cAACkB,EAAE,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAE6B,OAAO,EAAE;cAAI;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGN8B,KAAK,iBACJjD,OAAA,CAACkD,KAAK;MAACC,QAAQ,EAAC,SAAS;MAACtC,EAAE,EAAE;QAAEkB,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EACrCqB;IAAK;MAAAjC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAG,KAAK,iBACJtB,OAAA,CAAClB,IAAI;MAAC+B,EAAE,EAAE;QAAEkB,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eAClB5B,OAAA,CAACjB,WAAW;QAAA6C,QAAA,gBACV5B,OAAA,CAAChB,UAAU;UAACuD,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAb,QAAA,EAAC;QAEtC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnB,OAAA,CAACnB,IAAI;UAACqD,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAP,QAAA,gBACzB5B,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACtB5B,OAAA,CAACd,GAAG;cAAC8D,SAAS,EAAC,QAAQ;cAAApB,QAAA,gBACrB5B,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACxB,KAAK,EAAC,SAAS;gBAAAa,QAAA,EACrCN,KAAK,CAAC8B,cAAc,KAAK,IAAI,GAAG,GAAG,GAAG;cAAG;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,OAAO;gBAAAX,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACtB5B,OAAA,CAACd,GAAG;cAAC8D,SAAS,EAAC,QAAQ;cAAApB,QAAA,gBACrB5B,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACxB,KAAK,EAAC,SAAS;gBAAAa,QAAA,EACrCN,KAAK,CAAC+B,aAAa,KAAK,IAAI,GAAG,GAAG,GAAG;cAAG;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,OAAO;gBAAAX,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACtB5B,OAAA,CAACd,GAAG;cAAC8D,SAAS,EAAC,QAAQ;cAAApB,QAAA,gBACrB5B,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACxB,KAAK,EAAC,SAAS;gBAAAa,QAAA,GACrC0B,IAAI,CAACC,KAAK,CAACjC,KAAK,CAACkC,MAAM,GAAG,EAAE,CAAC,EAAC,GACjC;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,OAAO;gBAAAX,QAAA,EAAC;cAAc;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACtB5B,OAAA,CAACd,GAAG;cAAC8D,SAAS,EAAC,QAAQ;cAAApB,QAAA,gBACrB5B,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACxB,KAAK,EAAC,SAAS;gBAAAa,QAAA,EACrCN,KAAK,CAACmC;cAAY;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,OAAO;gBAAAX,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,eAGDnB,OAAA,CAAChB,UAAU;MAACuD,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAACO,SAAS,EAAC,QAAQ;MAACnC,EAAE,EAAE;QAAEkB,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EAAC;IAEvF;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbnB,OAAA,CAACnB,IAAI;MAACqD,SAAS;MAACC,OAAO,EAAE,CAAE;MAACtB,EAAE,EAAE;QAAEkB,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EACvCnB,QAAQ,CAACiD,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3B5D,OAAA,CAACnB,IAAI;QAACuD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACwB,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eAC9B5B,OAAA,CAAClB,IAAI;UAAC+B,EAAE,EAAE;YACRkD,MAAM,EAAE,MAAM;YACdvC,OAAO,EAAE,MAAM;YACfwC,aAAa,EAAE,QAAQ;YACvBrC,SAAS,EAAE,OAAO,CAAC;UACrB,CAAE;UAAAC,QAAA,eACA5B,OAAA,CAACjB,WAAW;YAAC8B,EAAE,EAAE;cACfoD,QAAQ,EAAE,CAAC;cACXjB,SAAS,EAAE,QAAQ;cACnBxB,OAAO,EAAE,MAAM;cACfwC,aAAa,EAAE,QAAQ;cACvBvC,cAAc,EAAE,eAAe,CAAC;YAClC,CAAE;YAAAG,QAAA,gBACA5B,OAAA,CAACd,GAAG;cAAA0C,QAAA,gBACF5B,OAAA,CAACd,GAAG;gBAAC2B,EAAE,EAAE;kBAAEkB,EAAE,EAAE;gBAAE,CAAE;gBAAAH,QAAA,EAChB+B,OAAO,CAAC/C;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNnB,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,IAAI;gBAACC,SAAS,EAAC,IAAI;gBAACC,YAAY;gBAAAb,QAAA,EACjD+B,OAAO,CAACjD;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACbnB,OAAA,CAAChB,UAAU;gBAACuD,OAAO,EAAC,OAAO;gBAACxB,KAAK,EAAC,gBAAgB;gBAACF,EAAE,EAAE;kBAAEkB,EAAE,EAAE;gBAAE,CAAE;gBAAAH,QAAA,EAC9D+B,OAAO,CAAChD;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EACZwC,OAAO,CAACrC,KAAK,iBACZtB,OAAA,CAACb,IAAI;gBACH2D,KAAK,EAAEa,OAAO,CAACrC,KAAM;gBACrBP,KAAK,EAAC,SAAS;gBACfwB,OAAO,EAAC,UAAU;gBAClBT,IAAI,EAAC,OAAO;gBACZjB,EAAE,EAAE;kBAAEkB,EAAE,EAAE;gBAAE;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNnB,OAAA,CAACf,MAAM;cACLsD,OAAO,EAAC,WAAW;cACnB2B,OAAO,EAAEP,OAAO,CAACvC,MAAO;cACxB+C,SAAS;cACTrC,IAAI,EAAC,OAAO;cACZjB,EAAE,EAAE;gBAAEuD,EAAE,EAAE;cAAE,CAAE,CAAC;cAAA;cAAAxC,QAAA,EAEd+B,OAAO,CAACtC;YAAU;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA5C6ByC,KAAK;QAAA5C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6CrC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGNkD,UAAU,iBACTrE,OAAA,CAAClB,IAAI;MAAA8C,QAAA,eACH5B,OAAA,CAACjB,WAAW;QAAA6C,QAAA,gBACV5B,OAAA,CAAChB,UAAU;UAACuD,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAb,QAAA,EAAC;QAEtC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;UAACuD,OAAO,EAAC,OAAO;UAACxB,KAAK,EAAC,gBAAgB;UAACF,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EAAC;QAElE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnB,OAAA,CAACsE,OAAO;UAACzD,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BnB,OAAA,CAACnB,IAAI;UAACqD,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAP,QAAA,gBACzB5B,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvB5B,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,WAAW;cAACxB,KAAK,EAAC,gBAAgB;cAAAa,QAAA,EAAC;YAEvD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,IAAI;cAACxB,KAAK,EAAC,SAAS;cAAAa,QAAA,EACrC,EAAAzB,qBAAA,GAAAkE,UAAU,CAACE,aAAa,cAAApE,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BqE,gBAAgB,cAAApE,sBAAA,uBAA1CA,sBAAA,CAA4CqE,cAAc,CAAC,OAAO,CAAC,KAAI;YAAK;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvB5B,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,WAAW;cAACxB,KAAK,EAAC,gBAAgB;cAAAa,QAAA,EAAC;YAEvD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,IAAI;cAACxB,KAAK,EAAC,SAAS;cAAAa,QAAA,EACrC,CAAAvB,sBAAA,GAAAgE,UAAU,CAACE,aAAa,cAAAlE,sBAAA,eAAxBA,sBAAA,CAA0BqE,SAAS,GAClC,IAAIpB,IAAI,CAACC,KAAK,CAACc,UAAU,CAACE,aAAa,CAACG,SAAS,CAAC,CAACD,cAAc,CAAC,OAAO,CAAC,EAAE,GAC5E;YAAK;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvB5B,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,WAAW;cAACxB,KAAK,EAAC,gBAAgB;cAAAa,QAAA,EAAC;YAEvD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,IAAI;cAACxB,KAAK,EAAC,SAAS;cAAAa,QAAA,EACrC,CAAAtB,sBAAA,GAAA+D,UAAU,CAACE,aAAa,cAAAjE,sBAAA,eAAxBA,sBAAA,CAA0BqE,QAAQ,GACjC,GAAGrB,IAAI,CAACC,KAAK,CAACc,UAAU,CAACE,aAAa,CAACI,QAAQ,CAAC,KAAK,GACrD;YAAK;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPnB,OAAA,CAACnB,IAAI;YAACuD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvB5B,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,WAAW;cAACxB,KAAK,EAAC,gBAAgB;cAAAa,QAAA,EAAC;YAEvD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,UAAU;cAACuD,OAAO,EAAC,IAAI;cAACxB,KAAK,EAAC,SAAS;cAAAa,QAAA,EACrC,CAAArB,sBAAA,GAAA8D,UAAU,CAACE,aAAa,cAAAhE,sBAAA,eAAxBA,sBAAA,CAA0BqE,SAAS,GAClCP,UAAU,CAACE,aAAa,CAACK,SAAS,CAACC,OAAO,CAAC,CAAC,CAAC,GAC7C;YAAK;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjB,EAAA,CAvPID,QAAQ;EAAA,QACKH,WAAW;AAAA;AAAAgF,EAAA,GADxB7E,QAAQ;AAyPd,eAAeA,QAAQ;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}